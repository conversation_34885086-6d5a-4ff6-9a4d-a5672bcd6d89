# Backend API URL
# This should point to your backend server
VITE_API_URL=http://localhost:3001

# Google OAuth Client ID (if using Google authentication)
# VITE_GOOGLE_CLIENT_ID=your_google_client_id_here

# WebSocket Server Configuration for Voice Chat
VITE_WEBSOCKET_URL=ws://localhost:3001

# For production, use your deployed server URL:
# VITE_WEBSOCKET_URL=wss://your-server.com

# Chat Mode Configuration
# Options: 'local' (browser-only) or 'internet' (real server)
VITE_CHAT_MODE=internet

# TURN Server Configuration (optional - for better connectivity)
# VITE_TURN_SERVER_URL=turn:your-turn-server.com:3478
# VITE_TURN_USERNAME=your-username
# VITE_TURN_CREDENTIAL=your-password

# Agora Voice Chat Configuration
VITE_AGORA_APP_ID=b6cee8251e324457be191e8b1a0ef090

# Other environment variables can be added here
# Remember: Only variables prefixed with VITE_ are exposed to the client
