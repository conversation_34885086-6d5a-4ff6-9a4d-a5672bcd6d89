# Authentication & Profile API Usage

This document explains how to use the authentication system and profile API endpoints in your Wolzyn Apparels application.

## Overview

The authentication system provides:
- User profile management via `/auth/profile` endpoint
- Token-based authentication with automatic refresh
- React hooks for easy state management
- Type-safe API calls with error handling

## Backend API Endpoints

Your backend should implement these endpoints:

### GET /auth/profile
Retrieves the current user's profile data.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "picture": "https://example.com/avatar.jpg",
    "phone": "+1234567890",
    "authMethod": "google",
    "isVerified": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "preferences": {
      "theme": "ascension",
      "notifications": true,
      "newsletter": false
    }
  }
}
```

### PUT /auth/profile
Updates the current user's profile data.

**Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Body:**
```json
{
  "name": "Updated Name",
  "phone": "+1234567890",
  "preferences": {
    "theme": "ember",
    "notifications": false,
    "newsletter": true
  }
}
```

### POST /auth/refresh
Refreshes the authentication token.

### POST /auth/logout
Logs out the user and invalidates the token.

### GET /auth/verify
Verifies if the current token is valid.

## Frontend Usage

### 1. Using the useAuth Hook (Recommended)

```tsx
import React from 'react';
import { useAuth } from '../hooks/useAuth';

const MyComponent: React.FC = () => {
  const { 
    user, 
    isLoading, 
    isAuthenticated, 
    error, 
    updateProfile, 
    logout 
  } = useAuth();

  const handleUpdateName = async () => {
    try {
      await updateProfile({ name: 'New Name' });
      console.log('Profile updated successfully');
    } catch (error) {
      console.error('Update failed:', error);
    }
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!isAuthenticated) return <div>Please log in</div>;

  return (
    <div>
      <h1>Welcome, {user?.name}</h1>
      <button onClick={handleUpdateName}>Update Name</button>
      <button onClick={logout}>Logout</button>
    </div>
  );
};
```

### 2. Using the API Service Directly

```tsx
import { authApi } from '../services/api';

// Get user profile
const fetchProfile = async () => {
  try {
    const profile = await authApi.getProfile();
    console.log('User profile:', profile);
  } catch (error) {
    console.error('Failed to fetch profile:', error);
  }
};

// Update profile
const updateUserProfile = async () => {
  try {
    const updatedProfile = await authApi.updateProfile({
      name: 'New Name',
      preferences: {
        theme: 'ember',
        notifications: true
      }
    });
    console.log('Updated profile:', updatedProfile);
  } catch (error) {
    console.error('Failed to update profile:', error);
  }
};
```

### 3. Using the AuthService (Higher-level API)

```tsx
import { AuthService } from '../services/authService';

// Get profile with error handling
const getProfile = async () => {
  const result = await AuthService.getUserProfile();
  
  if (result.success) {
    console.log('Profile:', result.data);
  } else {
    console.error('Error:', result.error);
  }
};

// Update profile with validation
const updateProfile = async () => {
  const result = await AuthService.updateUserProfile({
    email: '<EMAIL>',
    name: 'New Name'
  });
  
  if (result.success) {
    console.log('Profile updated:', result.data);
  } else {
    console.error('Update failed:', result.error);
  }
};
```

### 4. Making Custom Authenticated Requests

```tsx
import { makeAuthenticatedRequest } from '../services/authService';

// Custom API call
const fetchUserOrders = async () => {
  const result = await makeAuthenticatedRequest('/api/user/orders');
  
  if (result.success) {
    console.log('Orders:', result.data);
  } else {
    console.error('Failed to fetch orders:', result.error);
  }
};
```

## Environment Configuration

Set your backend API URL in your environment variables:

```env
# .env
REACT_APP_API_URL=http://localhost:3001
```

## Error Handling

The system provides comprehensive error handling:

- **401 Unauthorized**: Token expired or invalid - automatically clears local auth data
- **403 Forbidden**: Access denied
- **Network errors**: Connection issues
- **Validation errors**: Invalid data format

## Token Management

Tokens are automatically:
- Stored in secure HTTP-only cookies
- Included in API requests
- Refreshed when needed
- Cleared on logout or expiration

## Profile Component Example

The `Profile.tsx` component demonstrates:
- Loading user profile from backend
- Editing profile information
- Handling errors and loading states
- Updating preferences
- Real-time profile refresh

## Security Considerations

1. **HTTPS**: Always use HTTPS in production
2. **Token Storage**: Tokens are stored in secure cookies
3. **CSRF Protection**: Use sameSite cookie settings
4. **Input Validation**: Validate all user inputs
5. **Error Messages**: Don't expose sensitive information in error messages

## Testing

Test your authentication flow:

1. **Login**: Verify token is stored and profile is fetched
2. **Profile Update**: Test updating various profile fields
3. **Token Refresh**: Test automatic token renewal
4. **Logout**: Verify all auth data is cleared
5. **Error Handling**: Test with invalid tokens and network errors
