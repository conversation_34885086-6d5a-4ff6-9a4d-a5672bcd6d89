import React, { useState, useRef, useEffect } from 'react';
import './VRViewer.scss';

const VRViewer: React.FC = () => {
  const [videoUrl, setVideoUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isVRMode, setIsVRMode] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Initialize A-Frame scene when component mounts
  useEffect(() => {
    // Dynamically load A-Frame if not already loaded
    if (!window.AFRAME) {
      const script = document.createElement('script');
      script.src = 'https://aframe.io/releases/1.4.0/aframe.min.js';
      script.onload = () => {
        console.log('A-Frame loaded successfully');
      };
      document.head.appendChild(script);
    }
  }, []);

  const handleVideoLoad = () => {
    setIsLoading(false);
    setError(null);
  };

  const handleVideoError = () => {
    setIsLoading(false);
    setError('Failed to load video. Please check the URL and try again.');
  };

  const handleLoadVideo = () => {
    if (!videoUrl.trim()) {
      setError('Please enter a valid video URL');
      return;
    }

    setIsLoading(true);
    setError(null);
  };

  const enterVRMode = () => {
    setIsVRMode(true);
    if (containerRef.current) {
      containerRef.current.requestFullscreen?.();
    }
  };

  const exitVRMode = () => {
    setIsVRMode(false);
    if (document.fullscreenElement) {
      document.exitFullscreen?.();
    }
  };

  const sampleVideos = [
    {
      name: '360° Nature Video',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
    },
    {
      name: '360° City Tour',
      url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
    }
  ];

  return (
    <div className="vr-viewer">
      <div className="vr-viewer__header">
        <h1>🥽 VR Video Viewer</h1>
        <p>Enter a video URL to view in VR mode</p>
      </div>

      {/* URL Input Section */}
      <div className="vr-viewer__controls">
        <div className="vr-viewer__input-group">
          <input
            type="url"
            value={videoUrl}
            onChange={(e) => setVideoUrl(e.target.value)}
            placeholder="Enter video URL (MP4, WebM, etc.)"
            className="vr-viewer__input"
          />
          <button
            onClick={handleLoadVideo}
            disabled={isLoading || !videoUrl.trim()}
            className="vr-viewer__load-btn"
          >
            {isLoading ? 'Loading...' : 'Load Video'}
          </button>
        </div>

        {/* Sample Videos */}
        <div className="vr-viewer__samples">
          <h3>Sample Videos:</h3>
          <div className="vr-viewer__sample-buttons">
            {sampleVideos.map((sample, index) => (
              <button
                key={index}
                onClick={() => setVideoUrl(sample.url)}
                className="vr-viewer__sample-btn"
              >
                {sample.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="vr-viewer__error">
          <p>{error}</p>
        </div>
      )}

      {/* VR Scene Container */}
      <div 
        ref={containerRef}
        className={`vr-viewer__scene-container ${isVRMode ? 'vr-viewer__scene-container--vr' : ''}`}
      >
        {videoUrl && (
          <>
            {/* Regular Video Player */}
            {!isVRMode && (
              <div className="vr-viewer__video-container">
                <video
                  ref={videoRef}
                  src={videoUrl}
                  controls
                  crossOrigin="anonymous"
                  onLoadedData={handleVideoLoad}
                  onError={handleVideoError}
                  className="vr-viewer__video"
                >
                  Your browser does not support the video tag.
                </video>
                <div className="vr-viewer__video-controls">
                  <button
                    onClick={enterVRMode}
                    className="vr-viewer__vr-btn"
                  >
                    🥽 Enter VR Mode
                  </button>
                </div>
              </div>
            )}

            {/* A-Frame VR Scene */}
            {isVRMode && (
              <a-scene
                embedded
                style={{ height: '100%', width: '100%' }}
                vr-mode-ui="enabled: true"
                background="color: #000"
              >
                {/* Assets */}
                <a-assets>
                  <video
                    id="vr-video"
                    src={videoUrl}
                    autoPlay
                    loop
                    crossOrigin="anonymous"
                  />
                </a-assets>

                {/* 360 Video Sphere */}
                <a-videosphere
                  src="#vr-video"
                  rotation="0 180 0"
                />

                {/* Camera with look controls */}
                <a-camera
                  look-controls="enabled: true"
                  wasd-controls="enabled: false"
                  position="0 1.6 0"
                >
                  {/* Cursor for interaction */}
                  <a-cursor
                    animation__click="property: scale; startEvents: click; from: 0.1 0.1 0.1; to: 1 1 1; dur: 150"
                    animation__fusing="property: scale; startEvents: fusing; from: 1 1 1; to: 0.1 0.1 0.1; dur: 1500"
                    raycaster="objects: .clickable"
                    geometry="primitive: ring; radiusInner: 0.02; radiusOuter: 0.03"
                    material="color: white; shader: flat"
                  />
                </a-camera>

                {/* Exit VR Button */}
                <a-text
                  value="Exit VR"
                  position="0 2 -3"
                  align="center"
                  color="white"
                  class="clickable"
                  geometry="primitive: plane; width: 2; height: 0.5"
                  material="color: rgba(0,0,0,0.7)"
                  events={{
                    click: exitVRMode
                  }}
                />

                {/* Lighting */}
                <a-light type="ambient" color="#404040" />
              </a-scene>
            )}
          </>
        )}

        {/* Instructions when no video loaded */}
        {!videoUrl && (
          <div className="vr-viewer__placeholder">
            <div className="vr-viewer__placeholder-content">
              <h2>🎬 No Video Loaded</h2>
              <p>Enter a video URL above to start viewing in VR</p>
              <div className="vr-viewer__features">
                <div className="vr-viewer__feature">
                  <span className="vr-viewer__feature-icon">🥽</span>
                  <span>Immersive VR Experience</span>
                </div>
                <div className="vr-viewer__feature">
                  <span className="vr-viewer__feature-icon">🎮</span>
                  <span>Look Around Controls</span>
                </div>
                <div className="vr-viewer__feature">
                  <span className="vr-viewer__feature-icon">📱</span>
                  <span>Mobile VR Support</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* VR Instructions */}
      <div className="vr-viewer__instructions">
        <h3>🎯 How to Use</h3>
        <ol>
          <li>Enter a video URL or select a sample video</li>
          <li>Click "Load Video" to preview the video</li>
          <li>Click "Enter VR Mode" for immersive viewing</li>
          <li>Use mouse/touch to look around in VR mode</li>
          <li>Click "Exit VR" or press ESC to return</li>
        </ol>
        
        <div className="vr-viewer__compatibility">
          <h4>📱 VR Device Support</h4>
          <ul>
            <li>Oculus Quest/Rift</li>
            <li>HTC Vive</li>
            <li>Google Cardboard</li>
            <li>Samsung Gear VR</li>
            <li>Desktop browsers (mouse look)</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default VRViewer;
