import React from 'react';
import './UserAvatar.scss';

export interface GoogleUser {
  googleId: string;
  email: string;
  name: string;
  picture?: string;
}

export interface UserData {
  id: string;
  email?: string;
  phone?: string;
  name?: string;
  picture?: string;
  authMethod: 'email' | 'phone' | 'google';
  isVerified: boolean;
}

interface UserAvatarProps {
  user: UserData | GoogleUser;
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  className?: string;
  showOnlineStatus?: boolean;
  onClick?: () => void;
  alt?: string;
}

const UserAvatar: React.FC<UserAvatarProps> = ({
  user,
  size = 'medium',
  className = '',
  showOnlineStatus = false,
  onClick,
  alt
}) => {
  // Get user's initials for fallback
  const getInitials = (name?: string): string => {
    if (!name) return 'U';
    
    const names = name.trim().split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    }
    
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  };

  // Get display name
  const getDisplayName = (): string => {
    return user.name || user.email || 'User';
  };

  // Handle image load error
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    target.style.display = 'none';
    
    // Show the fallback placeholder
    const placeholder = target.nextElementSibling as HTMLElement;
    if (placeholder) {
      placeholder.style.display = 'flex';
    }
  };

  const avatarClasses = [
    'user-avatar',
    `user-avatar--${size}`,
    onClick ? 'user-avatar--clickable' : '',
    className
  ].filter(Boolean).join(' ');

  const initials = getInitials(user.name);
  const displayName = getDisplayName();

  return (
    <div 
      className={avatarClasses}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      aria-label={alt || `${displayName}'s avatar`}
    >
      {user.picture ? (
        <>
          <img
            src={user.picture}
            alt={alt || displayName}
            className="user-avatar__image"
            onError={handleImageError}
          />
          <div 
            className="user-avatar__placeholder" 
            style={{ display: 'none' }}
          >
            {initials}
          </div>
        </>
      ) : (
        <div className="user-avatar__placeholder">
          {initials}
        </div>
      )}
      
      {showOnlineStatus && (
        <div className="user-avatar__status user-avatar__status--online" />
      )}
    </div>
  );
};

export default UserAvatar;
