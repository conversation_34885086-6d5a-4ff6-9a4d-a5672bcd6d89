import React from 'react';
import { motion } from 'framer-motion';

const StorytellingBottomSection = ({ 
  product, 
  isInView, 
  tileVariants, 
  y3, 
  y4 
}) => {
  return (
    <div className="storytelling-product__grid-bottom">
      {/* Tile 3: The Outcome - from bottom left */}
      <motion.div 
        className="storytelling-product__tile storytelling-product__tile--outcome"
        variants={tileVariants.bottomLeft}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        style={{ y: y3 }}
        whileHover={{ scale: 1.02 }}
        transition={{ scale: { duration: 0.3, ease: "easeInOut" } }}
      >
        <h2 className="storytelling-product__outcome-title">{product.outcome.title || "What This Symbolizes"}</h2>
        <div className="storytelling-product__keywords">
          {product.outcome.keywords.map((keyword, index) => (
            <motion.span 
              key={index} 
              className="storytelling-product__keyword"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ 
                duration: 0.5, 
                delay: 0.2 + (index * 0.1)
              }}
            >
              {keyword}
            </motion.span>
          ))}
        </div>
        
        <div className="storytelling-product__details">
          <div className="storytelling-product__detail">
            <h3>Materials</h3>
            <p>{product.materials}</p>
          </div>
          <div className="storytelling-product__detail">
            <h3>Care</h3>
            <p>{product.care}</p>
          </div>
        </div>
      </motion.div>

      {/* Tile 4: The Compliment - from bottom right */}
      <motion.div 
        className="storytelling-product__tile storytelling-product__tile--compliment"
        variants={tileVariants.bottomRight}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        style={{ y: y4 }}
        whileHover={{ scale: 1.02 }}
        transition={{ scale: { duration: 0.3, ease: "easeInOut" } }}
      >
        <div className="storytelling-product__compliment-container">
          {product.compliment.symbol && (
            <motion.div 
              className="storytelling-product__symbol"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
              transition={{ 
                duration: 0.6, 
                delay: 0.7,
                type: "spring",
                stiffness: 100,
                damping: 10
              }}
            >
              <span className={`icon-${product.compliment.symbol}`}></span>
            </motion.div>
          )}
          <motion.div 
            className="storytelling-product__compliment-text"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ 
              duration: 0.6, 
              delay: 0.8
            }}
          >
            {product.compliment.text}
          </motion.div>
          {product.dropTheme && (
            <motion.div 
              className="storytelling-product__drop-theme"
              initial={{ opacity: 0 }}
              animate={isInView ? { opacity: 1 } : { opacity: 0 }}
              transition={{ 
                duration: 0.6, 
                delay: 0.9
              }}
            >
              From the {product.dropTheme} collection
            </motion.div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default StorytellingBottomSection;