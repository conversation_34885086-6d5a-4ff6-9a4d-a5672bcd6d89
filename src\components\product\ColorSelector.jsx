import React from 'react';
import './ColorSelector.scss';

const ColorSelector = ({ colors, selectedColor, onColorSelect, onImageChange, hideLabel = false }) => {
  // Color names mapping for accessibility and display
  const colorNames = {
    'black': 'Black',
    'white': 'White',
    'gray': 'Gray',
    'navy': 'Navy Blue',
    'red': 'Red',
    'green': 'Green',
    'blue': 'Blue',
    'purple': 'Purple',
    'yellow': 'Yellow',
    'orange': 'Orange',
    'pink': 'Pink',
    'teal': 'Teal'
  };

  // Handle color selection and image change
  const handleColorSelect = (color) => {
    onColorSelect(color);
    
    // Generate image name based on color or use default
    const imageName = color ? `${color}.png` : 'product.png';
    if (onImageChange) {
      onImageChange(imageName);
    }
  };

  return (
    <div className="color-selector">
      {!hideLabel && <h3 className="color-selector__title">Color</h3>}
      
      <div className="color-selector__options">
        {colors.map(color => (
          <button 
            key={color} 
            className={`color-selector__btn ${selectedColor === color ? 'color-selector__btn--selected' : ''}`}
            onClick={() => handleColorSelect(color)}
            aria-label={`Select color ${colorNames[color] || color}`}
            title={colorNames[color] || color}
          >
            <span 
              className="color-selector__swatch" 
              style={{ backgroundColor: color }}
            ></span>
          </button>
        ))}
      </div>
      
      {selectedColor && !hideLabel && (
        <div className="color-selector__selected-name">
          {colorNames[selectedColor] || selectedColor}
        </div>
      )}
    </div>
  );
};

export default ColorSelector;



