.fog-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
  pointer-events: none;
}

.fog-img {
  position: absolute;
  height: 100vh;
  width: 300vw;
  background-size: cover;
  background-position: center;
  background-repeat: repeat-x;
  opacity: 0.4;
  
  &-first {
    background-image: url('../../assets/images/fog-1.png');
    animation: fogAnimation 60s linear infinite;
  }
  
  &-second {
    background-image: url('../../assets/images/fog-2.png');
    animation: fogAnimation-reverse 45s linear infinite;
  }
}

@keyframes fogAnimation {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0%);
  }
}

@keyframes fogAnimation-reverse {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

