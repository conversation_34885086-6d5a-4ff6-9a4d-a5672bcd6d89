@use "sass:color";
@use './styles/variables' as *;
@use './styles/mixins' as *;

.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding-top: 6rem; // Add padding to account for fixed header
  
  @include tablet {
    padding-top: 7rem;
  }
}

// Global button styles
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 2px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &-primary {
    background-color: var(--accent-primary);
    color: white;
    
    &:hover {
      background-color: var(--accent-secondary);
    }
  }
  
  &-secondary {
    background-color: transparent;
    border: 1px solid var(--text-primary);
    color: var(--text-primary);
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

