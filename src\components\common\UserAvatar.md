# UserAvatar Component

A reusable React component for displaying user avatars with support for profile pictures and fallback initials.

## Features

- **Profile Picture Display**: Shows user's profile picture when available
- **Fallback Initials**: Displays user's initials when no picture is provided
- **Multiple Sizes**: Small, medium, large, and extra-large variants
- **Responsive Design**: Adapts to different screen sizes
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Online Status**: Optional online/offline status indicator
- **Error Handling**: Graceful fallback when images fail to load
- **TypeScript Support**: Full type safety with interfaces

## Interfaces

### GoogleUser
```typescript
interface GoogleUser {
  googleId: string;
  email: string;
  name: string;
  picture?: string; // Optional profile picture URL
}
```

### UserData
```typescript
interface UserData {
  id: string;
  email?: string;
  phone?: string;
  name?: string;
  picture?: string;
  authMethod: 'email' | 'phone' | 'google';
  isVerified: boolean;
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `user` | `UserData \| GoogleUser` | Required | User object containing profile information |
| `size` | `'small' \| 'medium' \| 'large' \| 'extra-large'` | `'medium'` | Size of the avatar |
| `className` | `string` | `''` | Additional CSS classes |
| `showOnlineStatus` | `boolean` | `false` | Whether to show online status indicator |
| `onClick` | `() => void` | `undefined` | Click handler for interactive avatars |
| `alt` | `string` | Auto-generated | Alt text for the image |

## Usage Examples

### Basic Usage with GoogleUser
```tsx
import UserAvatar, { GoogleUser } from './UserAvatar';

const googleUser: GoogleUser = {
  googleId: 'google_123456789',
  email: '<EMAIL>',
  name: 'John Doe',
  picture: 'https://lh3.googleusercontent.com/a/default-user=s96-c'
};

<UserAvatar user={googleUser} size="medium" />
```

### GoogleUser without Picture (Shows Initials)
```tsx
const googleUserNoImage: GoogleUser = {
  googleId: 'google_987654321',
  email: '<EMAIL>',
  name: 'Jane Smith'
  // picture is optional and omitted
};

<UserAvatar user={googleUserNoImage} size="large" />
```

### Interactive Avatar with Click Handler
```tsx
<UserAvatar 
  user={googleUser} 
  size="medium"
  onClick={() => console.log('Avatar clicked!')}
  className="header-avatar"
/>
```

### Avatar with Online Status
```tsx
<UserAvatar 
  user={googleUser} 
  size="large"
  showOnlineStatus={true}
/>
```

### Converting GoogleUser to UserData
```tsx
// When user signs in with Google, convert to UserData format
const convertGoogleUser = (googleUser: GoogleUser): UserData => ({
  id: googleUser.googleId,
  email: googleUser.email,
  name: googleUser.name,
  picture: googleUser.picture,
  authMethod: 'google',
  isVerified: true
});

const userData = convertGoogleUser(googleUser);
<UserAvatar user={userData} size="medium" />
```

## Size Variants

- **Small**: 1.75rem (28px) - For compact UI elements
- **Medium**: 2.5rem (40px) - Default size for headers and navigation
- **Large**: 3.5rem (56px) - For user profiles and mobile navigation
- **Extra Large**: 5rem (80px) - For profile pages and detailed views

## Styling

The component uses CSS custom properties for theming:

```scss
.user-avatar {
  --accent-primary: #your-primary-color;
  --accent-secondary: #your-secondary-color;
  --background-primary: #your-background-color;
}
```

## Accessibility

- Proper `alt` attributes for images
- ARIA labels for screen readers
- Keyboard navigation support with `tabIndex`
- Focus indicators for interactive avatars

## Error Handling

- Automatic fallback to initials when image fails to load
- Graceful handling of missing user names
- Default placeholder for users without names

## Integration with Header Component

The UserAvatar component is already integrated into the Header component:

```tsx
// Desktop navigation
<UserAvatar
  user={user}
  size="medium"
  onClick={toggleUserMenu}
  className="user-icon-button"
  alt="User menu"
/>

// Mobile navigation
<UserAvatar
  user={user}
  size="large"
  className="mobile-nav__user-avatar"
  alt="User profile"
/>
```

## Best Practices

1. **Always provide a name**: Even if optional, names improve the fallback experience
2. **Use appropriate sizes**: Match the avatar size to the UI context
3. **Handle loading states**: Consider showing a loading animation for slow images
4. **Optimize images**: Use appropriately sized profile pictures for better performance
5. **Test fallbacks**: Ensure the component works well without profile pictures
