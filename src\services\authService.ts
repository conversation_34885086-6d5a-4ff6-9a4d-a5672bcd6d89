import { authApi, UserProfile, ApiError } from './api';

/**
 * Authentication service with higher-level functions
 * This service provides convenient methods for common auth operations
 */

export interface AuthServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export class AuthService {
  /**
   * Get user profile with error handling
   */
  static async getUserProfile(): Promise<AuthServiceResult<UserProfile>> {
    try {
      const profile = await authApi.getProfile();
      return {
        success: true,
        data: profile
      };
    } catch (error) {
      console.error('AuthService: Failed to get user profile:', error);
      
      let errorMessage = 'Failed to load user profile';
      if (error instanceof ApiError) {
        errorMessage = error.message;
      }
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Update user profile with validation
   */
  static async updateUserProfile(
    updates: Partial<UserProfile>
  ): Promise<AuthServiceResult<UserProfile>> {
    try {
      // Basic validation
      if (updates.email && !this.isValidEmail(updates.email)) {
        return {
          success: false,
          error: 'Invalid email format'
        };
      }

      if (updates.phone && !this.isValidPhone(updates.phone)) {
        return {
          success: false,
          error: 'Invalid phone number format'
        };
      }

      const updatedProfile = await authApi.updateProfile(updates);
      return {
        success: true,
        data: updatedProfile
      };
    } catch (error) {
      console.error('AuthService: Failed to update user profile:', error);
      
      let errorMessage = 'Failed to update profile';
      if (error instanceof ApiError) {
        errorMessage = error.message;
      }
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Check if user is authenticated
   */
  static async isAuthenticated(): Promise<boolean> {
    try {
      return await authApi.verifyToken();
    } catch (error) {
      console.error('AuthService: Token verification failed:', error);
      return false;
    }
  }

  /**
   * Logout user
   */
  static async logout(): Promise<AuthServiceResult<void>> {
    try {
      await authApi.logout();
      return { success: true };
    } catch (error) {
      console.error('AuthService: Logout failed:', error);
      
      let errorMessage = 'Logout failed';
      if (error instanceof ApiError) {
        errorMessage = error.message;
      }
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Refresh authentication token
   */
  static async refreshAuth(): Promise<AuthServiceResult<void>> {
    try {
      await authApi.refreshToken();
      return { success: true };
    } catch (error) {
      console.error('AuthService: Token refresh failed:', error);
      
      let errorMessage = 'Failed to refresh authentication';
      if (error instanceof ApiError) {
        errorMessage = error.message;
      }
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Validate email format
   */
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format
   */
  private static isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[0-9]{10,15}$/;
    return phoneRegex.test(phone.replace(/[\s()-]/g, ''));
  }
}

/**
 * Hook-like function for components that need auth data
 * This is a simpler alternative to the useAuth hook for basic needs
 */
export const getAuthData = async () => {
  const profileResult = await AuthService.getUserProfile();
  const isAuth = await AuthService.isAuthenticated();
  
  return {
    user: profileResult.data || null,
    isAuthenticated: isAuth,
    error: profileResult.error || null
  };
};

/**
 * Utility function to make authenticated API calls
 * Use this for custom API endpoints that require authentication
 */
export const makeAuthenticatedRequest = async <T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<AuthServiceResult<T>> => {
  try {
    const { default: apiFetch } = await import('./api');
    const response = await apiFetch<T>(endpoint, options);
    
    return {
      success: response.success,
      data: response.data
    };
  } catch (error) {
    console.error('AuthService: Authenticated request failed:', error);
    
    let errorMessage = 'Request failed';
    if (error instanceof ApiError) {
      errorMessage = error.message;
    }
    
    return {
      success: false,
      error: errorMessage
    };
  }
};

export default AuthService;
