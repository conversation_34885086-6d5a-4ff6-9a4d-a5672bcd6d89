@use '../../styles/variables' as *;
@use "sass:color";
@use '../../styles/common';

.footer {
  background-color: var(--bg-secondary);
  padding: $spacing-xl 0 $spacing-md;
  margin-top: $spacing-xl;
  
  &__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: $spacing-lg;
    
    @media (min-width: $breakpoint-md) {
      grid-template-columns: 2fr 1fr 1fr 2fr;
    }
  }
  
  &__brand {
    margin-bottom: $spacing-lg;
    
    p {
      margin: $spacing-sm 0 $spacing-md;
      color: var(--text-secondary);
    }
  }
  
  &__logo {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-sm;
    
    .icon-studio {
      font-size: 2rem;
      margin-right: $spacing-xs;
      color: var(--logo-icon-color);
    }
    
    &-text {
      font-family: $font-logo;
      font-size: 1.75rem;
      font-weight: 400;
      color: var(--logo-text-color);
      letter-spacing: 0.02em;
    }
  }
  
  &__social {
    display: flex;
    gap: $spacing-md;
    
    a {
      font-size: 1.5rem;
      color: var(--text-secondary);
      transition: color 0.3s ease;
      
      &:hover {
        color: var(--accent-primary);
      }
    }
  }
  
  &__heading {
    font-size: 1.25rem;
    margin-bottom: $spacing-md;
  }
  
  &__links {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
    
    a {
      color: var(--text-secondary);
      transition: color 0.3s ease;
      
      &:hover {
        color: var(--accent-primary);
      }
    }
  }
  
  &__newsletter {
    &-form {
      display: flex;
      margin-top: $spacing-sm;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    &-input {
      flex: 1;
      padding: $spacing-md;
      border: none;
      background-color: var(--bg-primary);
      color: var(--text-primary);
      font-size: 0.95rem;
      transition: all 0.3s ease;
      
      &:focus {
        outline: none;
        box-shadow: inset 0 0 0 2px var(--accent-primary);
      }
      
      &::placeholder {
        color: var(--text-secondary);
        opacity: 0.7;
      }
    }
    
    &-button {
      padding: $spacing-md $spacing-lg;
      background-color: var(--accent-primary);
      color: white;
      border: none;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.3s ease, transform 0.2s ease;
      
      &:hover {
        background-color: color.scale($color-oracle-blue, $lightness: -10%);
        transform: translateY(-1px);
      }
      
      &:active {
        transform: translateY(1px);
      }
    }
  }
  
  &__copyright {
    margin-top: $spacing-xl;
    padding-top: $spacing-md;
    border-top: 1px solid var(--text-secondary);
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
  }
}










