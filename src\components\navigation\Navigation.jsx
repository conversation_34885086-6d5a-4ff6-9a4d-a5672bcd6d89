import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import './Navigation.scss';

const Navigation = ({ changeTheme, currentTheme }) => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const location = useLocation();
  
  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);
  
  // Close menu when route changes
  useEffect(() => {
    setMenuOpen(false);
  }, [location]);
  
  // Toggle between themes
  const toggleTheme = () => {
    const nextTheme = currentTheme === 'ascension' 
      ? 'ember' 
      : currentTheme === 'ember' 
        ? 'spice' 
        : 'ascension';
    changeTheme(nextTheme);
  };
  
  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };
  
  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Collections', path: '/collections' },
    { name: 'Story', path: '/coming-soon' },
    { name: 'About', path: '/about' }
  ];
  
  return (
    <nav className={`navigation ${scrolled ? 'navigation--scrolled' : ''}`}>
      <div className="container navigation__container">
        <Link to="/" className="navigation__logo">
          <span className="navigation__logo-text">WolZyn</span>
        </Link>
        
        <div className="navigation__menu-toggle" onClick={toggleMenu}>
          <span className={`navigation__menu-icon ${menuOpen ? 'navigation__menu-icon--open' : ''}`}></span>
        </div>
        
        <motion.div 
          className={`navigation__menu ${menuOpen ? 'navigation__menu--open' : ''}`}
          initial={false}
          animate={menuOpen ? { x: 0 } : { x: '100%' }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <ul className="navigation__list">
            {navItems.map((item) => (
              <li key={item.name} className="navigation__item">
                <Link 
                  to={item.path} 
                  className={`navigation__link ${location.pathname === item.path ? 'navigation__link--active' : ''}`}
                >
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
          
          <button className="navigation__theme-toggle" onClick={toggleTheme}>
            <span className="navigation__theme-icon"></span>
            <span className="navigation__theme-text">Theme: {currentTheme}</span>
          </button>
        </motion.div>
      </div>
    </nav>
  );
};

export default Navigation;
