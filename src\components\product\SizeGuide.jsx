import React from 'react';
import './SizeGuide.scss';

const SizeGuide = ({ isOpen, onClose }) => {
  if (!isOpen) return null;
  
  // Size chart data
  const sizeChartData = [
    { size: 'XS', chest: '34-36"', length: '26"', sleeve: '23"' },
    { size: 'S', chest: '36-38"', length: '27"', sleeve: '24"' },
    { size: 'M', chest: '38-40"', length: '28"', sleeve: '25"' },
    { size: 'L', chest: '40-42"', length: '29"', sleeve: '26"' },
    { size: 'XL', chest: '42-44"', length: '30"', sleeve: '27"' },
    { size: 'XXL', chest: '44-46"', length: '31"', sleeve: '28"' },
    { size: 'XXXL', chest: '46-48"', length: '32"', sleeve: '29"' },
    { size: 'OVERSIZE', chest: '48-52"', length: '33"', sleeve: '30"' },
  ];

  return (
    <div className="size-guide-overlay">
      <div className="size-guide">
        <div className="size-guide__header">
          <h2 className="size-guide__title">Size Guide</h2>
          <button className="size-guide__close" onClick={onClose} aria-label="Close size guide">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
        
        <div className="size-guide__content">
          <p className="size-guide__description">
            Our garments are designed with a modern, slightly relaxed fit. For a more fitted look, we recommend sizing down. For an oversized look, size up or choose our OVERSIZE option.
          </p>
          
          <div className="size-guide__measurement-diagram">
            <img src="/assets/images/size-guide-diagram.svg" alt="Measurement diagram" />
            <div className="size-guide__measurement-points">
              <div className="size-guide__measurement-point" style={{ top: '25%', left: '50%' }}>
                <span>Chest</span>
              </div>
              <div className="size-guide__measurement-point" style={{ top: '60%', left: '20%' }}>
                <span>Length</span>
              </div>
              <div className="size-guide__measurement-point" style={{ top: '40%', left: '80%' }}>
                <span>Sleeve</span>
              </div>
            </div>
          </div>
          
          <div className="size-guide__table-container">
            <table className="size-guide__table">
              <thead>
                <tr>
                  <th>Size</th>
                  <th>Chest</th>
                  <th>Length</th>
                  <th>Sleeve</th>
                </tr>
              </thead>
              <tbody>
                {sizeChartData.map((row) => (
                  <tr key={row.size}>
                    <td>{row.size}</td>
                    <td>{row.chest}</td>
                    <td>{row.length}</td>
                    <td>{row.sleeve}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="size-guide__tips">
            <h3>How to Measure</h3>
            <ul>
              <li><strong>Chest:</strong> Measure around the fullest part of your chest, keeping the tape horizontal.</li>
              <li><strong>Length:</strong> Measure from the highest point of the shoulder to the bottom hem.</li>
              <li><strong>Sleeve:</strong> Measure from the shoulder seam to the end of the sleeve.</li>
            </ul>
          </div>
        </div>
        
        <div className="size-guide__footer">
          <p>Still unsure about your size? Contact our customer service team for assistance.</p>
          <button className="btn btn-primary" onClick={onClose}>Got It</button>
        </div>
      </div>
    </div>
  );
};

export default SizeGuide;