import React from 'react';
import { useNavigate } from 'react-router-dom';

interface ChatRoom {
  id: string;
  name: string;
  description: string;
  userCount: number;
}

const chatModes = [
  {
    id: 'internet',
    name: 'Internet Voice Chat',
    description: 'Connect with gamers worldwide via real server',
    icon: '🌐',
    route: '/internet-chat',
    features: ['Global connectivity', 'Real-time server', 'Cross-platform']
  },
  {
    id: 'local',
    name: 'Local Voice Chat',
    description: 'Browser-only chat for testing and demos',
    icon: '💻',
    route: '/chat',
    features: ['No server needed', 'Browser tabs only', 'Instant setup']
  }
];

const SimpleChatRoomSelector: React.FC = () => {
  const navigate = useNavigate();
  const handleJoinRoom = (route: string) => {
    navigate(route);
  };

  return (
    <div style={{
      padding: '2rem',
      maxWidth: '1200px',
      margin: '0 auto',
      fontFamily: 'system-ui, sans-serif'
    }}>
      <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
        <h1 style={{
          fontSize: '2.5rem',
          marginBottom: '1rem',
          color: 'var(--text-primary, #333)'
        }}>
          🎮 Gamers Voice Chat
        </h1>
        <p style={{
          fontSize: '1.125rem',
          color: 'var(--text-secondary, #666)',
          maxWidth: '600px',
          margin: '0 auto'
        }}>
          Choose your voice chat mode: Local browser-only or Internet-wide connectivity
        </p>
      </div>

      {/* Available Rooms */}
      <div style={{ marginBottom: '3rem' }}>
        <h2 style={{
          fontSize: '1.5rem',
          marginBottom: '1.5rem',
          color: 'var(--text-primary, #333)'
        }}>
          🎮 Choose Your Voice Chat Mode
        </h2>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
          gap: '2rem'
        }}>
          {chatModes.map((mode) => (
            <div
              key={mode.id}
              style={{
                backgroundColor: 'var(--bg-secondary, #f8f9fa)',
                borderRadius: '12px',
                padding: '2rem',
                border: '2px solid var(--border-color, #e9ecef)',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                position: 'relative'
              }}
              onClick={() => handleJoinRoom(mode.route)}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = mode.id === 'internet' ? '#28a745' : '#007bff';
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = 'var(--border-color, #e9ecef)';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '1rem'
              }}>
                <span style={{
                  fontSize: '2rem',
                  marginRight: '1rem'
                }}>
                  {mode.icon}
                </span>
                <h3 style={{
                  margin: 0,
                  fontSize: '1.5rem',
                  color: 'var(--text-primary, #333)'
                }}>
                  {mode.name}
                </h3>
              </div>
              <p style={{
                color: 'var(--text-secondary, #666)',
                lineHeight: 1.6,
                marginBottom: '1.5rem',
                fontSize: '1rem'
              }}>
                {mode.description}
              </p>
              <div style={{ marginBottom: '1.5rem' }}>
                {mode.features.map((feature, idx) => (
                  <div key={idx} style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: '0.5rem',
                    fontSize: '0.875rem',
                    color: 'var(--text-secondary, #666)'
                  }}>
                    <span style={{ marginRight: '0.5rem', color: '#28a745' }}>✓</span>
                    {feature}
                  </div>
                ))}
              </div>
              <button style={{
                width: '100%',
                padding: '1rem 1.5rem',
                backgroundColor: mode.id === 'internet' ? '#28a745' : '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                fontSize: '1rem'
              }}>
                {mode.id === 'internet' ? '🌐 Connect Worldwide' : '💻 Start Local Chat'}
              </button>
            </div>
          ))}
        </div>
      </div>



      {/* Quick Actions */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        justifyContent: 'center',
        flexWrap: 'wrap'
      }}>
        <button
          onClick={() => handleJoinRoom('/internet-chat')}
          style={{
            padding: '1rem 2rem',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontWeight: '600',
            fontSize: '1rem',
            cursor: 'pointer',
            minWidth: '150px'
          }}
        >
          🌐 Internet Chat
        </button>
        <button
          onClick={() => handleJoinRoom('/chat')}
          style={{
            padding: '1rem 2rem',
            backgroundColor: 'var(--accent-primary, #007bff)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontWeight: '600',
            fontSize: '1rem',
            cursor: 'pointer',
            minWidth: '150px'
          }}
        >
          💻 Local Chat
        </button>
        <button
          onClick={() => navigate('/chat-demo')}
          style={{
            padding: '1rem 2rem',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontWeight: '600',
            fontSize: '1rem',
            cursor: 'pointer',
            minWidth: '150px'
          }}
        >
          📋 How It Works
        </button>
        <button
          onClick={() => navigate('/')}
          style={{
            padding: '1rem 2rem',
            backgroundColor: 'transparent',
            color: 'var(--text-primary, #333)',
            border: '2px solid var(--border-color, #e9ecef)',
            borderRadius: '8px',
            fontWeight: '600',
            fontSize: '1rem',
            cursor: 'pointer',
            minWidth: '150px'
          }}
        >
          Back to Home
        </button>
      </div>
    </div>
  );
};

export default SimpleChatRoomSelector;
