import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface ChatRoom {
  id: string;
  name: string;
  description: string;
  userCount: number;
}

const defaultRooms: ChatRoom[] = [
  {
    id: 'general',
    name: 'General',
    description: 'General discussion for everyone',
    userCount: 12
  },
  {
    id: 'fashion',
    name: 'Fashion Talk',
    description: 'Discuss latest fashion trends',
    userCount: 8
  },
  {
    id: 'support',
    name: 'Customer Support',
    description: 'Get help with your orders',
    userCount: 3
  }
];

const SimpleChatRoomSelector: React.FC = () => {
  const navigate = useNavigate();
  const [customRoom, setCustomRoom] = useState('');

  const handleJoinRoom = (roomId: string) => {
    navigate(`/chat?room=${encodeURIComponent(roomId)}`);
  };

  const handleJoinCustomRoom = (e: React.FormEvent) => {
    e.preventDefault();
    if (customRoom.trim()) {
      const roomId = customRoom.trim().toLowerCase().replace(/[^a-z0-9-_]/g, '-');
      navigate(`/chat?room=${encodeURIComponent(roomId)}`);
    }
  };

  return (
    <div style={{ 
      padding: '2rem', 
      maxWidth: '1200px', 
      margin: '0 auto',
      fontFamily: 'system-ui, sans-serif'
    }}>
      <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
        <h1 style={{ 
          fontSize: '2.5rem', 
          marginBottom: '1rem',
          color: 'var(--text-primary, #333)'
        }}>
          Join a Chat Room
        </h1>
        <p style={{ 
          fontSize: '1.125rem',
          color: 'var(--text-secondary, #666)',
          maxWidth: '600px',
          margin: '0 auto'
        }}>
          Connect with other Wolzyn Apparels community members
        </p>
      </div>

      {/* Available Rooms */}
      <div style={{ marginBottom: '3rem' }}>
        <h2 style={{ 
          fontSize: '1.5rem',
          marginBottom: '1.5rem',
          color: 'var(--text-primary, #333)'
        }}>
          Available Rooms
        </h2>
        <div style={{ 
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '1.5rem'
        }}>
          {defaultRooms.map((room) => (
            <div
              key={room.id}
              style={{
                backgroundColor: 'var(--bg-secondary, #f8f9fa)',
                borderRadius: '12px',
                padding: '1.5rem',
                border: '2px solid var(--border-color, #e9ecef)',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                position: 'relative'
              }}
              onClick={() => handleJoinRoom(room.id)}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = 'var(--accent-primary, #007bff)';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = 'var(--border-color, #e9ecef)';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <div style={{ 
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: '1rem'
              }}>
                <h3 style={{ 
                  margin: 0,
                  fontSize: '1.25rem',
                  color: 'var(--text-primary, #333)'
                }}>
                  #{room.name}
                </h3>
                <span style={{ 
                  fontSize: '0.875rem',
                  color: 'var(--text-secondary, #666)'
                }}>
                  {room.userCount} online
                </span>
              </div>
              <p style={{ 
                color: 'var(--text-secondary, #666)',
                lineHeight: 1.5,
                marginBottom: '1.5rem'
              }}>
                {room.description}
              </p>
              <button style={{
                width: '100%',
                padding: '0.75rem 1.5rem',
                backgroundColor: 'var(--accent-primary, #007bff)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}>
                Join Room
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Custom Room */}
      <div style={{
        backgroundColor: 'var(--bg-secondary, #f8f9fa)',
        borderRadius: '12px',
        padding: '2rem',
        border: '1px solid var(--border-color, #e9ecef)',
        marginBottom: '3rem'
      }}>
        <h2 style={{ 
          fontSize: '1.5rem',
          marginBottom: '1.5rem',
          color: 'var(--text-primary, #333)'
        }}>
          Create or Join Custom Room
        </h2>
        <form onSubmit={handleJoinCustomRoom} style={{ maxWidth: '500px' }}>
          <div style={{ 
            display: 'flex',
            gap: '1rem',
            marginBottom: '1rem',
            flexWrap: 'wrap'
          }}>
            <input
              type="text"
              value={customRoom}
              onChange={(e) => setCustomRoom(e.target.value)}
              placeholder="Enter room name..."
              style={{
                flex: 1,
                minWidth: '200px',
                padding: '0.75rem 1rem',
                border: '2px solid var(--border-color, #e9ecef)',
                borderRadius: '8px',
                backgroundColor: 'var(--bg-primary, white)',
                color: 'var(--text-primary, #333)',
                fontSize: '1rem'
              }}
              maxLength={30}
            />
            <button
              type="submit"
              disabled={!customRoom.trim()}
              style={{
                padding: '0.75rem 2rem',
                backgroundColor: 'var(--accent-primary, #007bff)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: customRoom.trim() ? 'pointer' : 'not-allowed',
                opacity: customRoom.trim() ? 1 : 0.5,
                whiteSpace: 'nowrap'
              }}
            >
              Join
            </button>
          </div>
          <p style={{ 
            color: 'var(--text-secondary, #666)',
            fontSize: '0.875rem',
            fontStyle: 'italic',
            margin: 0
          }}>
            Room names will be automatically formatted (lowercase, no special characters)
          </p>
        </form>
      </div>

      {/* Quick Actions */}
      <div style={{ 
        display: 'flex',
        gap: '1rem',
        justifyContent: 'center',
        flexWrap: 'wrap'
      }}>
        <button
          onClick={() => handleJoinRoom('general')}
          style={{
            padding: '1rem 2rem',
            backgroundColor: 'var(--accent-primary, #007bff)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontWeight: '600',
            fontSize: '1rem',
            cursor: 'pointer',
            minWidth: '150px'
          }}
        >
          Quick Join General
        </button>
        <button
          onClick={() => navigate('/')}
          style={{
            padding: '1rem 2rem',
            backgroundColor: 'transparent',
            color: 'var(--text-primary, #333)',
            border: '2px solid var(--border-color, #e9ecef)',
            borderRadius: '8px',
            fontWeight: '600',
            fontSize: '1rem',
            cursor: 'pointer',
            minWidth: '150px'
          }}
        >
          Back to Home
        </button>
      </div>
    </div>
  );
};

export default SimpleChatRoomSelector;
