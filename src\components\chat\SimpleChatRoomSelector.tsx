import React from 'react';
import { useNavigate } from 'react-router-dom';

interface ChatRoom {
  id: string;
  name: string;
  description: string;
  userCount: number;
}

const defaultRooms: ChatRoom[] = [
  {
    id: 'gamers',
    name: 'Gamers',
    description: 'Voice chat with fellow gamers',
    userCount: 5
  }
];

const SimpleChatRoomSelector: React.FC = () => {
  const navigate = useNavigate();
  const handleJoinRoom = () => {
    navigate('/chat');
  };

  return (
    <div style={{
      padding: '2rem',
      maxWidth: '1200px',
      margin: '0 auto',
      fontFamily: 'system-ui, sans-serif'
    }}>
      <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
        <h1 style={{
          fontSize: '2.5rem',
          marginBottom: '1rem',
          color: 'var(--text-primary, #333)'
        }}>
          🎮 Browser Voice Chat
        </h1>
        <p style={{
          fontSize: '1.125rem',
          color: 'var(--text-secondary, #666)',
          maxWidth: '600px',
          margin: '0 auto'
        }}>
          No backend required! Voice chat using browser capabilities only.
        </p>
      </div>

      {/* Available Rooms */}
      <div style={{ marginBottom: '3rem' }}>
        <h2 style={{
          fontSize: '1.5rem',
          marginBottom: '1.5rem',
          color: 'var(--text-primary, #333)'
        }}>
          🎮 Gamers Room
        </h2>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '1.5rem'
        }}>
          {defaultRooms.map((room) => (
            <div
              key={room.id}
              style={{
                backgroundColor: 'var(--bg-secondary, #f8f9fa)',
                borderRadius: '12px',
                padding: '1.5rem',
                border: '2px solid var(--border-color, #e9ecef)',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                position: 'relative'
              }}
              onClick={() => handleJoinRoom()}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = 'var(--accent-primary, #007bff)';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = 'var(--border-color, #e9ecef)';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: '1rem'
              }}>
                <h3 style={{
                  margin: 0,
                  fontSize: '1.25rem',
                  color: 'var(--text-primary, #333)'
                }}>
                  #{room.name}
                </h3>
                <span style={{
                  fontSize: '0.875rem',
                  color: 'var(--text-secondary, #666)'
                }}>
                  {room.userCount} online
                </span>
              </div>
              <p style={{
                color: 'var(--text-secondary, #666)',
                lineHeight: 1.5,
                marginBottom: '1.5rem'
              }}>
                {room.description}
              </p>
              <button style={{
                width: '100%',
                padding: '0.75rem 1.5rem',
                backgroundColor: 'var(--accent-primary, #007bff)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}>
                Join Room
              </button>
            </div>
          ))}
        </div>
      </div>



      {/* Quick Actions */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        justifyContent: 'center',
        flexWrap: 'wrap'
      }}>
        <button
          onClick={() => handleJoinRoom()}
          style={{
            padding: '1rem 2rem',
            backgroundColor: 'var(--accent-primary, #007bff)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontWeight: '600',
            fontSize: '1rem',
            cursor: 'pointer',
            minWidth: '150px'
          }}
        >
          🎮 Join Voice Chat
        </button>
        <button
          onClick={() => navigate('/chat-demo')}
          style={{
            padding: '1rem 2rem',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontWeight: '600',
            fontSize: '1rem',
            cursor: 'pointer',
            minWidth: '150px'
          }}
        >
          📋 How It Works
        </button>
        <button
          onClick={() => navigate('/')}
          style={{
            padding: '1rem 2rem',
            backgroundColor: 'transparent',
            color: 'var(--text-primary, #333)',
            border: '2px solid var(--border-color, #e9ecef)',
            borderRadius: '8px',
            fontWeight: '600',
            fontSize: '1rem',
            cursor: 'pointer',
            minWidth: '150px'
          }}
        >
          Back to Home
        </button>
      </div>
    </div>
  );
};

export default SimpleChatRoomSelector;
