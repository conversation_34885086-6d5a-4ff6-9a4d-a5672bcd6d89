import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import fs from 'fs'
import path from 'path'

export default defineConfig({
  plugins: [
    react(),
    {
      name: 'html-transform',
      closeBundle() {
        // Path to the generated index.html
        const indexPath = path.resolve(__dirname, 'dist/index.html');
        const wolzynPath = path.resolve(__dirname, 'dist/wolzyn.html');
        
        // Check if index.html exists
        if (fs.existsSync(indexPath)) {
          // Read the content of index.html
          const content = fs.readFileSync(indexPath, 'utf8');
          
          // Write the content to wolzyn.html
          fs.writeFileSync(wolzynPath, content);
          
          // Create a copy of index.html for each route that needs direct access
          const routes = [
            'product',
            'collections',
            'about',
            'coming-soon',
            'signup',
            'login'
          ];
          
          // Create directories for each route
          routes.forEach(route => {
            const routeDir = path.resolve(__dirname, `dist/${route}`);
            if (!fs.existsSync(routeDir)) {
              fs.mkdirSync(routeDir, { recursive: true });
            }
            
            // For product routes, create a directory for each product ID (1-10 for example)
            if (route === 'product') {
              for (let i = 1; i <= 10; i++) {
                const productDir = path.resolve(routeDir, `${i}`);
                if (!fs.existsSync(productDir)) {
                  fs.mkdirSync(productDir, { recursive: true });
                }
                fs.writeFileSync(path.resolve(productDir, 'index.html'), content);
              }
            } else {
              // For other routes, just create an index.html
              fs.writeFileSync(path.resolve(routeDir, 'index.html'), content);
            }
          });
        }
      }
    },
    {
      name: 'copy-favicon-files',
      closeBundle() {
        // Copy favicon files to the dist directory
        const faviconSrcDir = path.resolve(__dirname, 'src/assets/favicon_io');
        const faviconDestDir = path.resolve(__dirname, 'dist');
        
        // Create the destination directory if it doesn't exist
        if (!fs.existsSync(faviconDestDir)) {
          fs.mkdirSync(faviconDestDir, { recursive: true });
        }
        
        // Copy each favicon file
        const faviconFiles = [
          'android-chrome-192x192.png',
          'android-chrome-512x512.png',
          'apple-touch-icon.png',
          'favicon-16x16.png',
          'favicon-32x32.png',
          'favicon.ico',
          'site.webmanifest'
        ];
        
        faviconFiles.forEach(file => {
          const srcPath = path.resolve(faviconSrcDir, file);
          const destPath = path.resolve(faviconDestDir, file);
          
          if (fs.existsSync(srcPath)) {
            fs.copyFileSync(srcPath, destPath);
          }
        });
      }
    }
  ],
  css: {
    preprocessorOptions: {
      scss: {
        // Silence Sass deprecation warnings
        quietDeps: true
      }
    }
  },
  build: {
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      output: {
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    outDir: 'dist'
  }
})





