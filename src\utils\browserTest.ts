// Browser capability tests for the voice chat system

export interface BrowserCapabilities {
  webrtc: boolean;
  mediaDevices: boolean;
  broadcastChannel: boolean;
  localStorage: boolean;
  audioContext: boolean;
  https: boolean;
}

export interface TestResult {
  supported: boolean;
  capabilities: BrowserCapabilities;
  warnings: string[];
  errors: string[];
}

export function testBrowserCapabilities(): TestResult {
  const capabilities: BrowserCapabilities = {
    webrtc: false,
    mediaDevices: false,
    broadcastChannel: false,
    localStorage: false,
    audioContext: false,
    https: false
  };

  const warnings: string[] = [];
  const errors: string[] = [];

  // Test WebRTC support
  try {
    capabilities.webrtc = !!(window.RTCPeerConnection || (window as any).webkitRTCPeerConnection);
    if (!capabilities.webrtc) {
      errors.push('WebRTC is not supported in this browser');
    }
  } catch (e) {
    errors.push('Error testing WebRTC support');
  }

  // Test MediaDevices API
  try {
    capabilities.mediaDevices = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    if (!capabilities.mediaDevices) {
      errors.push('MediaDevices API is not supported');
    }
  } catch (e) {
    errors.push('Error testing MediaDevices API');
  }

  // Test BroadcastChannel API
  try {
    capabilities.broadcastChannel = typeof BroadcastChannel !== 'undefined';
    if (!capabilities.broadcastChannel) {
      errors.push('BroadcastChannel API is not supported');
    }
  } catch (e) {
    errors.push('Error testing BroadcastChannel API');
  }

  // Test localStorage
  try {
    capabilities.localStorage = typeof Storage !== 'undefined' && !!localStorage;
    if (capabilities.localStorage) {
      // Test if we can actually write to localStorage
      localStorage.setItem('test', 'test');
      localStorage.removeItem('test');
    } else {
      errors.push('localStorage is not available');
    }
  } catch (e) {
    errors.push('localStorage is not accessible (may be disabled)');
  }

  // Test AudioContext
  try {
    capabilities.audioContext = !!(window.AudioContext || (window as any).webkitAudioContext);
    if (!capabilities.audioContext) {
      warnings.push('AudioContext is not supported (audio processing may be limited)');
    }
  } catch (e) {
    warnings.push('Error testing AudioContext support');
  }

  // Test HTTPS
  capabilities.https = window.location.protocol === 'https:' || window.location.hostname === 'localhost';
  if (!capabilities.https) {
    errors.push('HTTPS is required for microphone access (except on localhost)');
  }

  // Additional warnings
  if (capabilities.webrtc && !capabilities.https && window.location.hostname !== 'localhost') {
    warnings.push('Microphone access requires HTTPS in production');
  }

  const supported = capabilities.webrtc &&
    capabilities.mediaDevices &&
    capabilities.broadcastChannel &&
    capabilities.localStorage &&
    (capabilities.https || window.location.hostname === 'localhost');

  return {
    supported,
    capabilities,
    warnings,
    errors
  };
}

export function getBrowserInfo(): string {
  const userAgent = navigator.userAgent;

  if (userAgent.includes('Chrome')) {
    return 'Chrome';
  } else if (userAgent.includes('Firefox')) {
    return 'Firefox';
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    return 'Safari';
  } else if (userAgent.includes('Edge')) {
    return 'Edge';
  } else {
    return 'Unknown';
  }
}

export async function testMicrophoneAccess(): Promise<{ success: boolean; error?: string }> {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

    // Stop the stream immediately after testing
    stream.getTracks().forEach(track => track.stop());

    return { success: true };
  } catch (error) {
    let errorMessage = 'Unknown error';

    if (error instanceof Error) {
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Microphone access denied by user';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No microphone found';
      } else if (error.name === 'NotSupportedError') {
        errorMessage = 'Microphone access not supported';
      } else {
        errorMessage = error.message;
      }
    }

    return { success: false, error: errorMessage };
  }
}

export function logBrowserCapabilities(): void {
  const result = testBrowserCapabilities();
  const browser = getBrowserInfo();

  console.group('🔍 Browser Voice Chat Capabilities');
  console.log(`Browser: ${browser}`);
  console.log(`Overall Support: ${result.supported ? '✅ Supported' : '❌ Not Supported'}`);

  console.group('📋 Capabilities');
  Object.entries(result.capabilities).forEach(([key, value]) => {
    console.log(`${key}: ${value ? '✅' : '❌'}`);
  });
  console.groupEnd();

  if (result.warnings.length > 0) {
    console.group('⚠️ Warnings');
    result.warnings.forEach(warning => console.warn(warning));
    console.groupEnd();
  }

  if (result.errors.length > 0) {
    console.group('❌ Errors');
    result.errors.forEach(error => console.error(error));
    console.groupEnd();
  }

  console.groupEnd();
}

// Auto-run test in development
if (import.meta.env.DEV) {
  // Run test after a short delay to avoid blocking initial load
  setTimeout(logBrowserCapabilities, 1000);
}
