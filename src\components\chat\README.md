# Chat & Voice Call System

A comprehensive real-time chat and voice calling system for Wolzyn Apparels with WebSocket communication, WebRTC voice calls, and user management features.

## Features

### 🔥 **Core Features**
- **Real-time Chat**: Instant messaging with typing indicators
- **Voice Calls**: High-quality WebRTC voice communication
- **User Management**: Join/leave notifications, user list, admin controls
- **Room System**: Multiple chat rooms with custom room creation
- **Responsive Design**: Works on desktop and mobile devices

### 🎤 **Voice Call Features**
- Start/end voice calls
- Join/leave ongoing calls
- Mute/unmute microphone
- Deafen/undeafen audio
- Real-time call status and duration
- Multiple participants support

### 👥 **User Management**
- Username display with avatars
- Admin badge for administrators
- Kick user functionality (admin only)
- Online status indicators
- Call participation status

### 💬 **Chat Features**
- Real-time message delivery
- Typing indicators
- Message timestamps
- System messages for user events
- Message history
- Auto-scroll to latest messages

## Architecture

### **Frontend Components**
```
src/
├── pages/
│   └── Chat.tsx                 # Main chat page
├── components/
│   └── chat/
│       ├── ChatRoomSelector.tsx # Room selection interface
│       └── ChatRoomSelector.scss
├── hooks/
│   └── useChat.ts              # Chat state management hook
└── services/
    ├── websocket.ts            # WebSocket communication
    └── webrtc.ts              # WebRTC voice calling
```

### **Backend Requirements**
Your backend needs to implement WebSocket endpoints:

```
ws://localhost:3001/ws/chat/{roomId}
```

## Usage

### **1. Basic Chat Setup**

```tsx
import { useChat } from '../hooks/useChat';

const ChatComponent = () => {
  const {
    users,
    messages,
    isConnected,
    sendMessage,
    startCall,
    endCall,
    toggleMute
  } = useChat();

  // Connect to a room
  useEffect(() => {
    connect('general', 'MyUsername');
  }, []);

  return (
    <div>
      {/* Chat interface */}
    </div>
  );
};
```

### **2. Voice Call Integration**

```tsx
// Start a voice call
const handleStartCall = async () => {
  try {
    await startCall();
    console.log('Call started successfully');
  } catch (error) {
    console.error('Failed to start call:', error);
  }
};

// Join an existing call
const handleJoinCall = async () => {
  try {
    await joinCall();
    console.log('Joined call successfully');
  } catch (error) {
    console.error('Failed to join call:', error);
  }
};

// Mute/unmute microphone
const handleToggleMute = () => {
  toggleMute();
};
```

### **3. User Management**

```tsx
// Kick a user (admin only)
const handleKickUser = (userId: string) => {
  if (canKickUsers) {
    kickUser(userId);
  }
};

// Check if user can perform admin actions
const isAdmin = canKickUsers;
```

## WebSocket Message Types

### **User Management**
- `USER_JOIN` - User joins the room
- `USER_LEAVE` - User leaves the room
- `USER_LIST` - Current users in room
- `USER_KICKED` - User was kicked by admin

### **Chat Messages**
- `CHAT_MESSAGE` - Text message
- `TYPING_START` - User starts typing
- `TYPING_STOP` - User stops typing

### **Voice Call Signaling**
- `CALL_START` - Voice call started
- `CALL_END` - Voice call ended
- `CALL_JOIN` - User joins call
- `CALL_LEAVE` - User leaves call

### **WebRTC Signaling**
- `OFFER` - WebRTC offer
- `ANSWER` - WebRTC answer
- `ICE_CANDIDATE` - ICE candidate

## Backend Implementation Example

### **WebSocket Server (Node.js)**

```javascript
const WebSocket = require('ws');

const wss = new WebSocket.Server({ port: 3001 });
const rooms = new Map(); // roomId -> Set of users

wss.on('connection', (ws, req) => {
  const roomId = extractRoomId(req.url);
  
  ws.on('message', (data) => {
    const message = JSON.parse(data);
    
    switch (message.type) {
      case 'USER_JOIN':
        handleUserJoin(ws, roomId, message.payload);
        break;
      case 'CHAT_MESSAGE':
        broadcastToRoom(roomId, message);
        break;
      case 'CALL_START':
        broadcastToRoom(roomId, message);
        break;
      // Handle other message types...
    }
  });
});

function broadcastToRoom(roomId, message) {
  const room = rooms.get(roomId);
  if (room) {
    room.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(message));
      }
    });
  }
}
```

## Configuration

### **Environment Variables**
```env
# WebSocket server URL
VITE_API_URL=http://localhost:3001

# STUN/TURN servers for WebRTC
VITE_STUN_SERVER=stun:stun.l.google.com:19302
VITE_TURN_SERVER=turn:your-turn-server.com
VITE_TURN_USERNAME=username
VITE_TURN_PASSWORD=password
```

### **WebRTC Configuration**
```typescript
const RTC_CONFIG: RTCConfiguration = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
    // Add TURN servers for production
    {
      urls: 'turn:your-turn-server.com',
      username: 'user',
      credential: 'pass'
    }
  ]
};
```

## Routes

- `/chat-rooms` - Chat room selection page
- `/chat?room=roomName` - Join specific chat room

## Security Considerations

1. **Authentication**: Users must be logged in to access chat
2. **Admin Verification**: Admin status checked server-side
3. **Rate Limiting**: Implement message rate limiting
4. **Content Filtering**: Filter inappropriate content
5. **TURN Servers**: Use TURN servers for NAT traversal in production

## Browser Compatibility

- **WebRTC**: Chrome 23+, Firefox 22+, Safari 11+
- **WebSockets**: All modern browsers
- **MediaDevices API**: HTTPS required for microphone access

## Troubleshooting

### **Common Issues**

1. **Microphone Access Denied**
   - Ensure HTTPS in production
   - Check browser permissions
   - Handle permission errors gracefully

2. **WebRTC Connection Failed**
   - Configure TURN servers for production
   - Check firewall settings
   - Verify ICE candidate exchange

3. **WebSocket Connection Issues**
   - Check backend WebSocket server
   - Verify CORS settings
   - Handle reconnection logic

### **Debug Mode**
Enable debug logging:
```typescript
// In browser console
localStorage.setItem('debug', 'chat:*');
```

## Performance Optimization

1. **Message Pagination**: Implement message history pagination
2. **User Limit**: Limit users per room for performance
3. **Audio Quality**: Adjust audio bitrate based on connection
4. **Reconnection**: Implement exponential backoff for reconnections

## Future Enhancements

- [ ] File sharing in chat
- [ ] Video calling support
- [ ] Screen sharing
- [ ] Message reactions
- [ ] Private messaging
- [ ] Chat moderation tools
- [ ] Message encryption
- [ ] Push notifications
