import React from 'react';
import { motion } from 'framer-motion';
import Header from '../header/Header';
import Footer from './Footer';
import './Layout.scss';

const Layout = ({ children, changeTheme, currentTheme }) => {
  return (
    <div className="layout">
      <Header changeTheme={changeTheme} currentTheme={currentTheme} />
      <main className="layout__main">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          {children}
        </motion.div>
      </main>
      <Footer />
    </div>
  );
};

export default Layout;

