import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const ProductTopSection = ({ product, productImage, fadeInVariants, slideUpVariants }) => {
  if (!product) return null;
  
  return (
    <div className="product-page__top-section">
      <div className="product-page__breadcrumb">
        <Link to="/">Home</Link> / <Link to="/collections">Collections</Link> / <span>{product.name}</span>
      </div>

      <div className="product-page__content">
        <motion.div 
          className="product-page__gallery"
          variants={slideUpVariants}
          layout
        >
          <div className="product-page__main-image">
            <img src={productImage} alt={product.name} />
            {product.isNew && <span className="product-page__badge product-page__badge--new">New</span>}
          </div>
        </motion.div>

        <motion.div 
          className="product-page__info"
          variants={slideUpVariants}
          layout
        >
          <div className="product-page__collection">{product.collection}</div>
          <h1 className="product-page__name">{product.name}</h1>
          <div className="product-page__price">${product.price}</div>

          <div className="product-page__description">
            <p>{product.longDescription}</p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ProductTopSection;