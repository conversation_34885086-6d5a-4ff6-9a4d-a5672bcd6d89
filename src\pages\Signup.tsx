import React, { useState, FormEvent, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import './Signup.scss';
import Cookies from 'js-cookie';
import { FcGoogle } from 'react-icons/fc';

// Add these type definitions
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          renderButton: (element: HTMLElement, options: any) => void;
          prompt: () => void;
        };
      };
    };
  }
}

// Simple email validation
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Simple phone validation (basic format check)
const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^\+?[0-9]{10,15}$/;
  return phoneRegex.test(phone);
};

// Cookie settings
const COOKIE_EXPIRES = 7; // days
const USER_COOKIE_NAME = 'wolzyn_user';

// Mock Google user data
interface GoogleUser {
  googleId: string;
  email: string;
  name: string;
  picture?: string;
}

// User data structure
interface UserData {
  id: string;
  email?: string;
  phone?: string;
  name?: string;
  picture?: string;
  authMethod: 'email' | 'phone' | 'google';
  isVerified: boolean;
}

const Signup: React.FC = () => {
  const navigate = useNavigate();
  const [contactInfo, setContactInfo] = useState('');
  const [contactType, setContactType] = useState<'email' | 'phone' | 'unknown'>('unknown');
  const [contactError, setContactError] = useState('');
  const [otpSent, setOtpSent] = useState(false);
  const [otp, setOtp] = useState('');
  const [otpError, setOtpError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if user is already logged in
  useEffect(() => {
    const userCookie = Cookies.get(USER_COOKIE_NAME);
    if (userCookie) {
      try {
        const userData = JSON.parse(userCookie);
        if (userData && userData.isVerified) {
          // Redirect to home or dashboard
          navigate('/');
        }
      } catch (error) {
        // Invalid cookie, clear it
        Cookies.remove(USER_COOKIE_NAME);
      }
    }
  }, [navigate]);

  // Detect if input is email or phone
  const handleContactChange = (value: string) => {
    setContactInfo(value);

    // Auto-detect if it's likely an email or phone
    if (value.includes('@')) {
      setContactType('email');
    } else if (/^[0-9+\s()-]{4,}$/.test(value)) {
      setContactType('phone');
    } else {
      setContactType('unknown');
    }

    // Clear error when typing
    if (contactError) setContactError('');
  };

  // Validate contact info
  const validateContact = (): boolean => {
    if (!contactInfo) {
      setContactError('Please enter your email or phone number');
      return false;
    }

    // Try to determine the type if still unknown
    let type = contactType;
    if (type === 'unknown') {
      if (isValidEmail(contactInfo)) {
        type = 'email';
        setContactType('email');
      } else if (isValidPhone(contactInfo)) {
        type = 'phone';
        setContactType('phone');
      }
    }

    if (type === 'email' && !isValidEmail(contactInfo)) {
      setContactError('Please enter a valid email address (e.g., <EMAIL>)');
      return false;
    }

    if (type === 'phone' && !isValidPhone(contactInfo)) {
      setContactError('Please enter a valid phone number (10-15 digits, can start with +)');
      return false;
    }

    if (type === 'unknown') {
      setContactError('Please enter a valid email or phone number');
      return false;
    }

    setContactError('');
    return true;
  };

  // Validate OTP
  const validateOtp = (): boolean => {
    if (!otp) {
      setOtpError('Please enter the verification code');
      return false;
    }

    if (otp.length !== 6 || !/^\d+$/.test(otp)) {
      setOtpError('Please enter a valid 6-digit code');
      return false;
    }

    setOtpError('');
    return true;
  };

  // Save user data to cookie
  const saveUserToCookie = (userData: UserData) => {
    Cookies.set(USER_COOKIE_NAME, JSON.stringify(userData), {
      expires: COOKIE_EXPIRES,
      sameSite: 'strict',
      secure: window.location.protocol === 'https:'
    });
  };

  // Request OTP
  const handleRequestOtp = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateContact()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call to send OTP
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log(`OTP requested for ${contactType}:`, contactInfo);

      // Store partial user data
      const userData: UserData = {
        id: `user_${Date.now()}`,
        authMethod: contactType as 'email' | 'phone',
        isVerified: false
      };

      if (contactType === 'email') {
        userData.email = contactInfo;
      } else if (contactType === 'phone') {
        userData.phone = contactInfo;
      }

      // Save partial user data
      saveUserToCookie(userData);

      setOtpSent(true);
    } catch (error) {
      console.error('OTP request error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Verify OTP and complete signup
  const handleVerifyOtp = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateOtp()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call to verify OTP
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Signup completed with OTP:', otp);

      // Get existing user data from cookie
      const userCookie = Cookies.get(USER_COOKIE_NAME);
      if (userCookie) {
        const userData: UserData = JSON.parse(userCookie);

        // Update user data
        userData.isVerified = true;

        // Save updated user data
        saveUserToCookie(userData);

        // Redirect to home or dashboard
        navigate('/');
      }
    } catch (error) {
      console.error('OTP verification error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle Google Sign-In response
  const handleGoogleCredentialResponse = async (response: any) => {
    setIsSubmitting(true);
    setGoogleAuthError(null);
    try {
      // Get the ID token from the response
      const idToken = response.credential;

      // In a real app, you would send this token to your backend for verification
      // Here we'll decode it client-side for demonstration purposes
      const tokenParts = idToken.split('.');
      if (tokenParts.length !== 3) {
        throw new Error('Invalid token format');
      }

      // Decode the payload (second part of the token)
      const payload = JSON.parse(atob(tokenParts[1].replace(/-/g, '+').replace(/_/g, '/')));

      // Extract user information from the token payload
      const googleUser: GoogleUser = {
        googleId: payload.sub,
        email: payload.email,
        name: payload.name || `${payload.given_name} ${payload.family_name}`,
        picture: payload.picture
      };

      console.log('Google authentication successful:', googleUser);

      // Create user data from Google response
      const userData: UserData = {
        id: googleUser.googleId,
        email: googleUser.email,
        name: googleUser.name,
        picture: googleUser.picture,
        authMethod: 'google',
        isVerified: true
      };

      // Save user data to cookie
      saveUserToCookie(userData);

      // Redirect to home or dashboard
      navigate('/');
    } catch (error) {
      console.error('Google authentication error:', error);
      setGoogleAuthError('Failed to authenticate with Google. Please try again or use another method.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Initialize Google Sign-In
  const googleButtonRef = useRef<HTMLDivElement>(null);
  const [googleAuthError, setGoogleAuthError] = useState<string | null>(null);

  useEffect(() => {
    // Load the Google Sign-In API script
    const loadGoogleScript = () => {
      if (window.google?.accounts) {
        initializeGoogleSignIn();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = initializeGoogleSignIn;
      script.onerror = () => {
        console.error('Failed to load Google Sign-In script');
        setGoogleAuthError('Google Sign-In is currently unavailable. Please try again later or use another method.');
      };
      document.body.appendChild(script);
    };

    // Initialize Google Sign-In after script loads
    const initializeGoogleSignIn = () => {
      if (!window.google?.accounts) {
        setGoogleAuthError('Google Sign-In failed to initialize. Please try again later.');
        return;
      }

      try {
        window.google.accounts.id.initialize({
          client_id: 'YOUR_GOOGLE_CLIENT_ID', // Replace with your actual Google Client ID
          callback: handleGoogleCredentialResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
          error_callback: (error: any) => {
            console.error('Google Sign-In error:', error);
            setGoogleAuthError('Google Sign-In encountered an error. Please try again later.');
          }
        });

        // Render the Google Sign-In button
        if (googleButtonRef.current) {
          window.google.accounts.id.renderButton(googleButtonRef.current, {
            type: 'standard',
            theme: 'outline',
            size: 'large',
            text: 'signup_with',
            shape: 'rectangular',
            logo_alignment: 'left',
            width: '100%',
          });
        }
      } catch (error) {
        console.error('Error initializing Google Sign-In:', error);
        setGoogleAuthError('Failed to initialize Google Sign-In. Please try another method.');
      }
    };

    // Add a timeout to detect if Google script is blocked
    const timeoutId = setTimeout(() => {
      if (!window.google?.accounts) {
        setGoogleAuthError('Google Sign-In may be blocked by your browser or network. Please check your settings or try another method.');
      }
    }, 5000);

    loadGoogleScript();

    // Clean up
    return () => {
      clearTimeout(timeoutId);
      const scriptTag = document.querySelector('script[src="https://accounts.google.com/gsi/client"]');
      if (scriptTag) {
        scriptTag.remove();
      }
    };
  }, []);

  // Handle Google signup
  const handleGoogleSignup = () => {
    setGoogleAuthError(null);

    // Redirect to your backend Google auth endpoint
    try {
      const backendAuthUrl = 'http://localhost:3001/auth/google';

      // Your backend will handle the redirect back to your frontend
      // with the token via /auth/success?token=xxx
      window.location.href = backendAuthUrl;
    } catch (error) {
      console.error('Error redirecting to Google auth:', error);
      setGoogleAuthError('Failed to connect to authentication service. Please try again later.');
    }
  };

  return (
    <div className="signup-page">
      <div className="signup-page__container fade-in">
        <div className="signup-page__decorative-elements">
          <div className="signup-page__decorative-circle signup-page__decorative-circle--top-right scale-in"></div>
          <div className="signup-page__decorative-circle signup-page__decorative-circle--bottom-left scale-in"></div>
        </div>

        <div className="signup-page__form-container">
          <div className="signup-page__logo stagger-item">
            <span className="icon-studio icon-Studio-Project-1"></span>
          </div>

          <div className="signup-page__header stagger-item">
            <h2>Join Wolzyn</h2>
            <p>Create your account to begin your journey</p>
          </div>

          {!otpSent ? (
            <form onSubmit={handleRequestOtp} className="signup-page__form">
              <div className="signup-page__input-group stagger-item">
                <input
                  id="contactInfo"
                  type="text"
                  placeholder="Email or phone number"
                  value={contactInfo}
                  onChange={(e) => handleContactChange(e.target.value)}
                  disabled={isSubmitting}
                />
                <label htmlFor="contactInfo">
                  Email or phone number
                </label>
                {contactError && (
                  <p className="error-message">{contactError}</p>
                )}
                <p className="hint-message">
                  Enter your email (<EMAIL>) or phone number (+**********)
                </p>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="signup-page__submit-button stagger-item"
              >
                {isSubmitting ? (
                  <div className="signup-page__spinner-container">
                    <svg className="spinner" viewBox="0 0 24 24">
                      <circle className="spinner__track" cx="12" cy="12" r="10" />
                      <circle className="spinner__path" cx="12" cy="12" r="10" />
                    </svg>
                    <span>Sending code...</span>
                  </div>
                ) : (
                  'Continue'
                )}
              </button>
            </form>
          ) : (
            <form onSubmit={handleVerifyOtp} className="signup-page__form">
              <div className="signup-page__verification-info stagger-item">
                <p>We've sent a verification code to:</p>
                <p className="signup-page__contact-display">{contactInfo}</p>
              </div>

              <div className="signup-page__input-group stagger-item">
                <input
                  id="otp"
                  type="text"
                  placeholder="Verification code"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  maxLength={6}
                  disabled={isSubmitting}
                />
                <label htmlFor="otp">Verification code</label>
                {otpError && (
                  <p className="error-message">{otpError}</p>
                )}
                <p className="hint-message">Enter the 6-digit code sent to your {contactType}</p>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="signup-page__submit-button stagger-item"
              >
                {isSubmitting ? (
                  <div className="signup-page__spinner-container">
                    <svg className="spinner" viewBox="0 0 24 24">
                      <circle className="spinner__track" cx="12" cy="12" r="10" />
                      <circle className="spinner__path" cx="12" cy="12" r="10" />
                    </svg>
                    <span>Verifying...</span>
                  </div>
                ) : (
                  'Create Account'
                )}
              </button>

              <button
                type="button"
                onClick={() => setOtpSent(false)}
                className="signup-page__back-button stagger-item"
              >
                Back
              </button>
            </form>
          )}

          <div className="signup-page__divider stagger-item">
            <span>Or continue with</span>
          </div>

          <div className="signup-page__google-container stagger-item">
            {isSubmitting ? (
              <button
                disabled
                className="signup-page__google-button"
              >
                <div className="signup-page__spinner-container">
                  <svg className="spinner" viewBox="0 0 24 24">
                    <circle className="spinner__track" cx="12" cy="12" r="10" />
                    <circle className="spinner__path" cx="12" cy="12" r="10" />
                  </svg>
                  <span>Connecting...</span>
                </div>
              </button>
            ) : (
              <button
                onClick={handleGoogleSignup}
                className="signup-page__google-button"
                type="button"
              >
                <FcGoogle className="signup-page__google-icon" />
                Sign up with Google
              </button>
            )}
          </div>

          <div className="signup-page__footer stagger-item">
            <p>
              Already have an account?{' '}
              <Link to="/login">Log in</Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Signup;












