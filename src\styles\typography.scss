@use './variables' as *;
@use "sass:color";

h1, h2, h3, h4, h5, h6 {
  font-family: $font-serif;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: $spacing-md;
}

h1 {
  font-size: 2.5rem;
  
  @media (min-width: $breakpoint-md) {
    font-size: 3.5rem;
  }
}

h2 {
  font-size: 2rem;
  
  @media (min-width: $breakpoint-md) {
    font-size: 2.5rem;
  }
}

h3 {
  font-size: 1.5rem;
  
  @media (min-width: $breakpoint-md) {
    font-size: 2rem;
  }
}

p {
  font-family: $font-primary;
  margin-bottom: $spacing-md;
  line-height: 1.6;
}

a {
  font-family: $font-primary;
  color: var(--accent-primary);
  text-decoration: none;
  transition: color 0.3s ease;
  
  &:hover {
    color: color.scale($color-oracle-blue, $lightness: -10%);
  }
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

.btn {
  font-family: $font-primary;
  font-weight: 500;
  display: inline-block;
  padding: $spacing-sm $spacing-lg;
  border-radius: 2px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &-primary {
    background-color: var(--accent-primary);
    color: $color-mythlight;
    
    &:hover {
      background-color: color.scale($color-oracle-blue, $lightness: -10%);
      color: $color-mythlight;
    }
  }
  
  &-secondary {
    background-color: transparent;
    color: var(--text-primary);
    border: 1px solid var(--text-primary);
    
    &:hover {
      background-color: var(--text-primary);
      color: var(--bg-primary);
    }
  }
}

// New class for editorial content
.editorial {
  font-family: $font-editorial;
  
  p {
    font-family: $font-editorial;
    font-size: 1.05rem;
    line-height: 1.7;
  }
  
  blockquote {
    font-family: $font-editorial;
    font-style: italic;
    border-left: 3px solid var(--accent-primary);
    padding-left: $spacing-md;
    margin-left: 0;
    margin-right: 0;
    color: var(--text-secondary);
  }
}

// Hero tagline specific styling
.hero__tagline {
  font-family: $font-serif;
  font-weight: 600;
}

// Navigation styling
.header__nav-link {
  font-family: $font-primary;
  font-weight: 500;
}


