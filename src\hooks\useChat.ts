import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './useAuth';
import WebSocketService, { MessageType, ChatUser, ChatMessage } from '../services/websocket';
import WebRTCService, { VoiceCallState } from '../services/webrtc';

interface ChatState {
  users: ChatUser[];
  messages: ChatMessage[];
  typingUsers: string[];
  isConnected: boolean;
  connectionError: string | null;
  currentUser: ChatUser | null;
}

interface UseChatReturn extends ChatState {
  // Connection methods
  connect: (roomId: string, username?: string) => Promise<void>;
  disconnect: () => void;
  
  // Chat methods
  sendMessage: (content: string) => void;
  startTyping: () => void;
  stopTyping: () => void;
  
  // Voice call methods
  startCall: () => Promise<void>;
  endCall: () => void;
  joinCall: () => Promise<void>;
  leaveCall: () => void;
  toggleMute: () => void;
  toggleDeafen: () => void;
  
  // User management
  kickUser: (userId: string) => void;
  
  // Call state
  callState: VoiceCallState;
  
  // Permissions
  canKickUsers: boolean;
}

export const useChat = (): UseChatReturn => {
  const { user } = useAuth();
  const [state, setState] = useState<ChatState>({
    users: [],
    messages: [],
    typingUsers: [],
    isConnected: false,
    connectionError: null,
    currentUser: null
  });
  
  const [callState, setCallState] = useState<VoiceCallState>({
    isInCall: false,
    isMuted: false,
    isDeafened: false,
    participants: [],
    callStartedBy: null,
    callStartTime: null
  });

  const wsServiceRef = useRef<WebSocketService | null>(null);
  const webrtcServiceRef = useRef<WebRTCService | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Connect to chat room
  const connect = useCallback(async (roomId: string, username?: string): Promise<void> => {
    if (!user) {
      throw new Error('User must be authenticated to join chat');
    }

    try {
      const chatUsername = username || user.name || `User_${user.id.slice(-4)}`;
      
      // Create WebSocket service
      wsServiceRef.current = new WebSocketService(roomId, user.id, chatUsername);
      
      // Set up WebSocket listeners
      setupWebSocketListeners();
      
      // Connect to WebSocket
      await wsServiceRef.current.connect();
      
      // Create WebRTC service
      webrtcServiceRef.current = new WebRTCService(wsServiceRef.current, user.id);
      
      // Set up WebRTC listeners
      setupWebRTCListeners();
      
      setState(prev => ({
        ...prev,
        isConnected: true,
        connectionError: null,
        currentUser: {
          id: user.id,
          username: chatUsername,
          isAdmin: user.email?.includes('admin') || false, // Simple admin check
          isInCall: false,
          isMuted: false,
          joinedAt: new Date()
        }
      }));
      
    } catch (error) {
      console.error('Failed to connect to chat:', error);
      setState(prev => ({
        ...prev,
        connectionError: error instanceof Error ? error.message : 'Connection failed'
      }));
      throw error;
    }
  }, [user]);

  // Disconnect from chat
  const disconnect = useCallback(() => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.endCall();
      webrtcServiceRef.current = null;
    }
    
    if (wsServiceRef.current) {
      wsServiceRef.current.disconnect();
      wsServiceRef.current = null;
    }
    
    setState(prev => ({
      ...prev,
      isConnected: false,
      users: [],
      messages: [],
      typingUsers: [],
      currentUser: null
    }));
    
    setCallState({
      isInCall: false,
      isMuted: false,
      isDeafened: false,
      participants: [],
      callStartedBy: null,
      callStartTime: null
    });
  }, []);

  // Set up WebSocket event listeners
  const setupWebSocketListeners = useCallback(() => {
    if (!wsServiceRef.current) return;

    const ws = wsServiceRef.current;

    // User management
    ws.on(MessageType.USER_LIST, (users: ChatUser[]) => {
      setState(prev => ({ ...prev, users }));
    });

    ws.on(MessageType.USER_JOIN, (userData: ChatUser) => {
      setState(prev => ({
        ...prev,
        users: [...prev.users.filter(u => u.id !== userData.id), userData],
        messages: [...prev.messages, {
          id: `system_${Date.now()}`,
          userId: 'system',
          username: 'System',
          content: `${userData.username} joined the chat`,
          timestamp: new Date(),
          type: 'system'
        }]
      }));
    });

    ws.on(MessageType.USER_LEAVE, (userData: { userId: string, username: string }) => {
      setState(prev => ({
        ...prev,
        users: prev.users.filter(u => u.id !== userData.userId),
        messages: [...prev.messages, {
          id: `system_${Date.now()}`,
          userId: 'system',
          username: 'System',
          content: `${userData.username} left the chat`,
          timestamp: new Date(),
          type: 'system'
        }]
      }));
    });

    ws.on(MessageType.USER_KICKED, (data: { userId: string, username: string, adminUsername: string }) => {
      setState(prev => ({
        ...prev,
        users: prev.users.filter(u => u.id !== data.userId),
        messages: [...prev.messages, {
          id: `system_${Date.now()}`,
          userId: 'system',
          username: 'System',
          content: `${data.username} was kicked by ${data.adminUsername}`,
          timestamp: new Date(),
          type: 'system'
        }]
      }));
    });

    // Chat messages
    ws.on(MessageType.CHAT_MESSAGE, (message: ChatMessage) => {
      setState(prev => ({
        ...prev,
        messages: [...prev.messages, message]
      }));
    });

    // Typing indicators
    ws.on(MessageType.TYPING_START, (data: { username: string }) => {
      setState(prev => ({
        ...prev,
        typingUsers: [...prev.typingUsers.filter(u => u !== data.username), data.username]
      }));
    });

    ws.on(MessageType.TYPING_STOP, (data: { username: string }) => {
      setState(prev => ({
        ...prev,
        typingUsers: prev.typingUsers.filter(u => u !== data.username)
      }));
    });

    // Error handling
    ws.on(MessageType.ERROR, (error: { message: string }) => {
      setState(prev => ({
        ...prev,
        connectionError: error.message
      }));
    });
  }, []);

  // Set up WebRTC event listeners
  const setupWebRTCListeners = useCallback(() => {
    if (!webrtcServiceRef.current) return;

    const webrtc = webrtcServiceRef.current;

    webrtc.on('stateChange', (newState: VoiceCallState) => {
      setCallState(newState);
      
      // Update user call status
      setState(prev => ({
        ...prev,
        users: prev.users.map(u => ({
          ...u,
          isInCall: newState.participants.includes(u.id),
          isMuted: u.id === user?.id ? newState.isMuted : u.isMuted
        }))
      }));
    });

    webrtc.on('error', (error: { type: string, message: string }) => {
      setState(prev => ({
        ...prev,
        connectionError: `Voice call error: ${error.message}`
      }));
    });
  }, [user?.id]);

  // Chat methods
  const sendMessage = useCallback((content: string) => {
    if (wsServiceRef.current && content.trim()) {
      wsServiceRef.current.sendChatMessage(content.trim());
    }
  }, []);

  const startTyping = useCallback(() => {
    if (wsServiceRef.current) {
      wsServiceRef.current.startTyping();
      
      // Auto-stop typing after 3 seconds
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      typingTimeoutRef.current = setTimeout(() => {
        stopTyping();
      }, 3000);
    }
  }, []);

  const stopTyping = useCallback(() => {
    if (wsServiceRef.current) {
      wsServiceRef.current.stopTyping();
    }
    
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
  }, []);

  // Voice call methods
  const startCall = useCallback(async () => {
    if (webrtcServiceRef.current) {
      await webrtcServiceRef.current.startCall();
    }
  }, []);

  const endCall = useCallback(() => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.endCall();
    }
  }, []);

  const joinCall = useCallback(async () => {
    if (webrtcServiceRef.current) {
      await webrtcServiceRef.current.joinCall();
    }
  }, []);

  const leaveCall = useCallback(() => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.leaveCall();
    }
  }, []);

  const toggleMute = useCallback(() => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.toggleMute();
    }
  }, []);

  const toggleDeafen = useCallback(() => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.toggleDeafen();
    }
  }, []);

  // User management
  const kickUser = useCallback((userId: string) => {
    if (wsServiceRef.current && state.currentUser?.isAdmin) {
      wsServiceRef.current.kickUser(userId);
    }
  }, [state.currentUser?.isAdmin]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  // Check if current user can kick others
  const canKickUsers = state.currentUser?.isAdmin || false;

  return {
    // State
    ...state,
    callState,
    
    // Methods
    connect,
    disconnect,
    sendMessage,
    startTyping,
    stopTyping,
    startCall,
    endCall,
    joinCall,
    leaveCall,
    toggleMute,
    toggleDeafen,
    kickUser,
    
    // Permissions
    canKickUsers
  };
};
