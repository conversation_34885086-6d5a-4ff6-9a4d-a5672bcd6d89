# Dependencies
/node_modules
/.pnp
.pnp.js
yarn.lock
package-lock.json

# Build outputs
/dist
/build
/.cache

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
/coverage

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Sass
.sass-cache/
*.css.map
*.sass.map
*.scss.map

# Parcel
.parcel-cache

# Vite
.vite/

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
