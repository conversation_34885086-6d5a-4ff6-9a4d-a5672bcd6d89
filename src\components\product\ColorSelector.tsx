import React from 'react';
import './ColorSelector.scss';

interface ColorSelectorProps {
  colors: string[];
  selectedColor: string;
  onColorSelect: (color: string) => void;
  onImageChange?: (imageName: string) => void;
  hideLabel?: boolean;
}

interface ColorNamesMap {
  [key: string]: string;
}

const ColorSelector: React.FC<ColorSelectorProps> = ({ 
  colors, 
  selectedColor, 
  onColorSelect, 
  onImageChange, 
  hideLabel = false 
}) => {
  // Color names mapping for accessibility and display
  const colorNames: ColorNamesMap = {
    'black': 'Black',
    'white': 'White',
    'gray': 'Gray',
    'navy': 'Navy Blue',
    'red': 'Red',
    'green': 'Green',
    'blue': 'Blue',
    'purple': 'Purple',
    'yellow': 'Yellow',
    'orange': 'Orange',
    'pink': 'Pink',
    'teal': 'Teal'
  };

  // Handle color selection and image change
  const handleColorSelect = (color: string): void => {
    onColorSelect(color);
    
    // Generate image name based on color or use default
    const imageName = color ? `${color}.png` : 'product.png';
    if (onImageChange) {
      onImageChange(imageName);
    }
  };

  return (
    <div className="color-selector">
      {!hideLabel && <span className="color-selector__label">Color: {colorNames[selectedColor] || selectedColor}</span>}
      <div className="color-selector__options">
        {colors.map(color => (
          <button
            key={color}
            className={`color-selector__option ${selectedColor === color ? 'color-selector__option--selected' : ''}`}
            style={{ backgroundColor: color }}
            onClick={() => handleColorSelect(color)}
            aria-label={`Select ${colorNames[color] || color} color`}
            aria-pressed={selectedColor === color}
          />
        ))}
      </div>
    </div>
  );
};

export default ColorSelector;