import WebSocketService, { MessageType } from './websocket';

// WebRTC configuration
const RTC_CONFIG: RTCConfiguration = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
    // Add TURN servers for production
    // { urls: 'turn:your-turn-server.com', username: 'user', credential: 'pass' }
  ]
};

export interface VoiceCallState {
  isInCall: boolean;
  isMuted: boolean;
  isDeafened: boolean;
  participants: string[];
  callStartedBy: string | null;
  callStartTime: Date | null;
}

export class WebRTCService {
  private peerConnections: Map<string, RTCPeerConnection> = new Map();
  private localStream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private gainNode: GainNode | null = null;
  private callState: VoiceCallState = {
    isInCall: false,
    isMuted: false,
    isDeafened: false,
    participants: [],
    callStartedBy: null,
    callStartTime: null
  };
  private listeners: Map<string, Set<Function>> = new Map();

  constructor(private wsService: WebSocketService, private userId: string) {
    this.setupWebSocketListeners();
    this.initializeListeners();
  }

  private initializeListeners() {
    const events = ['stateChange', 'participantJoined', 'participantLeft', 'error'];
    events.forEach(event => {
      this.listeners.set(event, new Set());
    });
  }

  private setupWebSocketListeners() {
    this.wsService.on(MessageType.CALL_START, this.handleCallStart.bind(this));
    this.wsService.on(MessageType.CALL_END, this.handleCallEnd.bind(this));
    this.wsService.on(MessageType.CALL_JOIN, this.handleCallJoin.bind(this));
    this.wsService.on(MessageType.CALL_LEAVE, this.handleCallLeave.bind(this));
    this.wsService.on(MessageType.OFFER, this.handleOffer.bind(this));
    this.wsService.on(MessageType.ANSWER, this.handleAnswer.bind(this));
    this.wsService.on(MessageType.ICE_CANDIDATE, this.handleIceCandidate.bind(this));
  }

  // Initialize audio stream
  async initializeAudio(): Promise<void> {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000
        },
        video: false
      });

      // Set up audio context for volume control
      this.audioContext = new AudioContext();
      const source = this.audioContext.createMediaStreamSource(this.localStream);
      this.gainNode = this.audioContext.createGain();
      source.connect(this.gainNode);
      this.gainNode.connect(this.audioContext.destination);

      console.log('Audio initialized successfully');
    } catch (error) {
      console.error('Error accessing microphone:', error);
      this.emit('error', { type: 'microphone', message: 'Could not access microphone' });
      throw error;
    }
  }

  // Start a voice call
  async startCall(): Promise<void> {
    try {
      if (!this.localStream) {
        await this.initializeAudio();
      }

      this.callState.isInCall = true;
      this.callState.callStartedBy = this.userId;
      this.callState.callStartTime = new Date();
      this.callState.participants = [this.userId];

      this.wsService.startCall();
      this.emit('stateChange', this.callState);
      
      console.log('Call started');
    } catch (error) {
      console.error('Error starting call:', error);
      this.emit('error', { type: 'call_start', message: 'Failed to start call' });
    }
  }

  // End the voice call
  endCall(): void {
    this.peerConnections.forEach((pc, userId) => {
      pc.close();
      this.peerConnections.delete(userId);
    });

    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.callState = {
      isInCall: false,
      isMuted: false,
      isDeafened: false,
      participants: [],
      callStartedBy: null,
      callStartTime: null
    };

    this.wsService.endCall();
    this.emit('stateChange', this.callState);
    
    console.log('Call ended');
  }

  // Join an existing call
  async joinCall(): Promise<void> {
    try {
      if (!this.localStream) {
        await this.initializeAudio();
      }

      this.callState.isInCall = true;
      this.wsService.joinCall();
      
      console.log('Joined call');
    } catch (error) {
      console.error('Error joining call:', error);
      this.emit('error', { type: 'call_join', message: 'Failed to join call' });
    }
  }

  // Leave the call
  leaveCall(): void {
    this.peerConnections.forEach((pc, userId) => {
      pc.close();
      this.peerConnections.delete(userId);
    });

    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    this.callState.isInCall = false;
    this.callState.participants = this.callState.participants.filter(id => id !== this.userId);

    this.wsService.leaveCall();
    this.emit('stateChange', this.callState);
    
    console.log('Left call');
  }

  // Mute/unmute microphone
  toggleMute(): void {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        this.callState.isMuted = !audioTrack.enabled;
        this.emit('stateChange', this.callState);
        console.log(`Microphone ${this.callState.isMuted ? 'muted' : 'unmuted'}`);
      }
    }
  }

  // Deafen/undeafen (mute all incoming audio)
  toggleDeafen(): void {
    if (this.gainNode) {
      this.callState.isDeafened = !this.callState.isDeafened;
      this.gainNode.gain.value = this.callState.isDeafened ? 0 : 1;
      this.emit('stateChange', this.callState);
      console.log(`Audio ${this.callState.isDeafened ? 'deafened' : 'undeafened'}`);
    }
  }

  // Create peer connection for a user
  private async createPeerConnection(userId: string): Promise<RTCPeerConnection> {
    const pc = new RTCPeerConnection(RTC_CONFIG);

    // Add local stream to peer connection
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => {
        pc.addTrack(track, this.localStream!);
      });
    }

    // Handle incoming stream
    pc.ontrack = (event) => {
      const [remoteStream] = event.streams;
      this.handleRemoteStream(userId, remoteStream);
    };

    // Handle ICE candidates
    pc.onicecandidate = (event) => {
      if (event.candidate) {
        this.wsService.sendIceCandidate(userId, event.candidate);
      }
    };

    // Handle connection state changes
    pc.onconnectionstatechange = () => {
      console.log(`Connection state with ${userId}:`, pc.connectionState);
      if (pc.connectionState === 'failed' || pc.connectionState === 'disconnected') {
        this.handlePeerDisconnection(userId);
      }
    };

    this.peerConnections.set(userId, pc);
    return pc;
  }

  // Handle remote audio stream
  private handleRemoteStream(userId: string, stream: MediaStream): void {
    const audio = new Audio();
    audio.srcObject = stream;
    audio.autoplay = true;
    audio.volume = this.callState.isDeafened ? 0 : 1;
    
    // Store audio element for volume control
    (audio as any).userId = userId;
    document.body.appendChild(audio);
    
    console.log(`Receiving audio from user ${userId}`);
  }

  // WebSocket event handlers
  private async handleCallStart(payload: any): Promise<void> {
    this.callState.isInCall = true;
    this.callState.callStartedBy = payload.userId;
    this.callState.callStartTime = new Date();
    this.emit('stateChange', this.callState);
  }

  private handleCallEnd(payload: any): void {
    this.endCall();
  }

  private async handleCallJoin(payload: any): Promise<void> {
    const { userId } = payload;
    if (userId !== this.userId && this.callState.isInCall) {
      this.callState.participants.push(userId);
      this.emit('participantJoined', userId);
      this.emit('stateChange', this.callState);

      // Create offer for new participant
      const pc = await this.createPeerConnection(userId);
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      this.wsService.sendOffer(userId, offer);
    }
  }

  private handleCallLeave(payload: any): void {
    const { userId } = payload;
    this.handlePeerDisconnection(userId);
  }

  private async handleOffer(payload: any): Promise<void> {
    const { userId, offer } = payload;
    const pc = await this.createPeerConnection(userId);
    
    await pc.setRemoteDescription(offer);
    const answer = await pc.createAnswer();
    await pc.setLocalDescription(answer);
    
    this.wsService.sendAnswer(userId, answer);
  }

  private async handleAnswer(payload: any): Promise<void> {
    const { userId, answer } = payload;
    const pc = this.peerConnections.get(userId);
    
    if (pc) {
      await pc.setRemoteDescription(answer);
    }
  }

  private async handleIceCandidate(payload: any): Promise<void> {
    const { userId, candidate } = payload;
    const pc = this.peerConnections.get(userId);
    
    if (pc) {
      await pc.addIceCandidate(candidate);
    }
  }

  private handlePeerDisconnection(userId: string): void {
    const pc = this.peerConnections.get(userId);
    if (pc) {
      pc.close();
      this.peerConnections.delete(userId);
    }

    // Remove audio element
    const audioElements = document.querySelectorAll('audio');
    audioElements.forEach(audio => {
      if ((audio as any).userId === userId) {
        audio.remove();
      }
    });

    this.callState.participants = this.callState.participants.filter(id => id !== userId);
    this.emit('participantLeft', userId);
    this.emit('stateChange', this.callState);
  }

  // Event system
  on(event: string, callback: Function): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.add(callback);
    }
  }

  off(event: string, callback: Function): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in WebRTC listener:', error);
        }
      });
    }
  }

  // Getters
  get state(): VoiceCallState {
    return { ...this.callState };
  }

  get isInCall(): boolean {
    return this.callState.isInCall;
  }

  get isMuted(): boolean {
    return this.callState.isMuted;
  }

  get isDeafened(): boolean {
    return this.callState.isDeafened;
  }
}

export default WebRTCService;
