@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.size-selector {
  margin-bottom: $spacing-lg;
  
  &__title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: $spacing-sm;
    color: var(--text-primary);
  }
  
  &__options {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-sm;
    margin-bottom: $spacing-sm;
  }
  
  &__btn {
    min-width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 $spacing-sm;
    border: 1px solid var(--border-color);
    background: transparent;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    // Futuristic hover effect
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: -1;
    }
    
    &:hover {
      border-color: var(--accent-primary);
      color: var(--accent-primary);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
    
    &--selected {
      background: var(--accent-primary);
      border-color: var(--accent-primary);
      color: white;
      
      &:hover {
        color: white;
      }
      
      // Pulsing animation for selected size
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 120%;
        height: 120%;
        background: var(--accent-primary);
        border-radius: 50%;
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.3;
        z-index: -1;
        animation: pulse 1.5s ease-out infinite;
      }
      
      @keyframes pulse {
        0% {
          transform: translate(-50%, -50%) scale(0);
          opacity: 0.3;
        }
        100% {
          transform: translate(-50%, -50%) scale(1);
          opacity: 0;
        }
      }
    }
    
    // Special styling for OVERSIZE
    &:last-child {
      background: linear-gradient(to right, rgba(var(--accent-primary-rgb), 0.1), rgba(var(--accent-secondary-rgb), 0.1));
      border: 1px dashed var(--accent-primary);
      font-style: italic;
    }
  }
  
  &__guide {
    margin-top: $spacing-md;
  }
  
  &__guide-btn {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
    background: transparent;
    border: none;
    padding: 0;
    cursor: pointer;
    transition: color 0.3s ease;
    
    &:hover {
      color: var(--accent-primary);
    }
  }
  
  &__guide-icon {
    display: inline-flex;
    margin-right: $spacing-xs;
  }
}