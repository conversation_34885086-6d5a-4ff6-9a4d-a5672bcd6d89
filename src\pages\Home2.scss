// Define variables and mixins directly in this file to ensure they're available
// These can be removed if your project already has these properly set up

// Variables
$font-primary: 'DM Sans', sans-serif;
$font-serif: 'Playfair Display', serif;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;

// Mixins
@mixin mobile {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1024px) {
    @content;
  }
}

// Main styles
.home2 {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary, #fdfdfd);
  overflow: hidden;
  
  // Vertical Navigation - Desktop
  &__nav {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    display: flex;
    align-items: center;
    padding-left: $spacing-xl;
    z-index: 10;
    
    @include mobile {
      display: none;
    }
    
    &-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    &-item {
      margin: $spacing-lg 0;
      
      &:first-child {
        margin-top: 0;
      }
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    &-link {
      position: relative;
      font-family: $font-primary;
      font-size: 1rem;
      font-weight: 400;
      letter-spacing: 0.2em;
      text-transform: uppercase;
      color: var(--text-primary, #000000);
      text-decoration: none;
      padding: 0.5rem 0;
      transition: color 0.3s ease;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        background-color: var(--text-primary, #000000);
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1);
      }
      
      &:hover {
        &::after {
          transform: scaleX(1);
          transform-origin: left;
        }
      }
    }
  }
  
  // Centered Logo
  &__logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    // Style for the icon font
    .icon-Studio-Project-1 {
      font-size: 4rem;
      color: var(--accent-primary, #000000);
      display: block;
      
      @include tablet {
        font-size: 5rem;
      }
      
      @include desktop {
        font-size: 8rem;
      }
    }
    
    &-text {
      font-family: $font-serif;
      font-size: 4rem;
      color: var(--text-primary, #000000);
      letter-spacing: 0.05em;
      margin-top: $spacing-md;
      
      @include tablet {
        font-size: 5rem;
      }
      
      @include desktop {
        font-size: 6rem;
      }
    }
  }
  
  // Mobile Navigation Trigger
  &__mobile-trigger {
    position: fixed;
    top: $spacing-lg;
    right: $spacing-lg;
    cursor: pointer;
    z-index: 20;
    padding: $spacing-sm;
    
    @include tablet {
      display: none;
    }
    
    &-text {
      font-family: $font-primary;
      font-size: 0.875rem;
      letter-spacing: 0.2em;
      text-transform: uppercase;
      color: var(--text-primary, #000000);
    }
  }
  
  // Mobile Navigation Panel
  &__mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: var(--bg-primary, #fdfdfd);
    padding: $spacing-xl 0;
    z-index: 15;
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.05);
    
    @include tablet {
      display: none;
    }
    
    &-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    &-item {
      margin: $spacing-md 0;
    }
    
    &-link {
      font-family: $font-primary;
      font-size: 1.125rem;
      font-weight: 400;
      letter-spacing: 0.2em;
      text-transform: uppercase;
      color: var(--text-primary, #000000);
      text-decoration: none;
      padding: 0.5rem 0;
    }
  }
}

