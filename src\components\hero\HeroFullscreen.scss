@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.hero-fullscreen {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  &__background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, #cfe3e3 0%, #f9bfa3 100%);
  }
  
  &__content {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: $spacing-md;
    max-width: 90%;
    width: 100%;
    margin: 0 auto;
    
    @include tablet {
      max-width: 85%;
      padding: $spacing-lg;
    }
    
    @include desktop {
      max-width: 900px;
      padding: $spacing-xl;
    }
  }
  
  &__logo {
    margin-bottom: $spacing-lg;
    
    @include tablet {
      margin-bottom: $spacing-lg;
    }
    
    // Style for the icon font
    .icon-Studio-Project-1 {
      font-size: 4rem;
      color: var(--accent-primary);
      display: block;
      
      @include tablet {
        font-size: 5rem;
      }
      
      @include desktop {
        font-size: 8rem;
      }
    }
  }
  
  &__tagline {
    font-family: $font-serif;
    font-size: 2rem;
    font-weight: 400;
    letter-spacing: 0.02em;
    color: var(--text-primary);
    margin-bottom: $spacing-sm;
    line-height: 1.2;
    
    @include tablet {
      font-size: 3rem;
      margin-bottom: $spacing-md;
    }
    
    @include desktop {
      font-size: 4.5rem;
    }
  }
  
  &__subheading {
    font-family: $font-primary;
    font-size: 1rem;
    font-weight: 300;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: var(--text-secondary);
    margin-bottom: $spacing-lg;
    
    @include tablet {
      font-size: 1.2rem;
      letter-spacing: 0.12em;
      margin-bottom: $spacing-xl;
    }
    
    @include desktop {
      font-size: 1.4rem;
      letter-spacing: 0.15em;
    }
  }
  
  &__cta {
    margin-top: $spacing-md;
    
    @include tablet {
      margin-top: $spacing-lg;
    }
  }
  
  &__button {
    position: relative;
    display: inline-block;
    padding: 0.75rem 1.5rem;
    font-family: $font-primary;
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 0.08em;
    text-transform: uppercase;
    color: var(--text-primary);
    background-color: transparent;
    border: 1px solid var(--text-primary);
    overflow: hidden;
    transition: all 0.3s ease;
    
    @include tablet {
      padding: 0.85rem 2rem;
      font-size: 0.95rem;
    }
    
    @include desktop {
      padding: 1rem 2.5rem;
      font-size: 1rem;
      letter-spacing: 0.1em;
    }
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background-color: var(--text-primary);
      transform: scaleX(0);
      transform-origin: right;
      transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1);
    }
    
    &:hover {
      background-color: rgba(45, 49, 66, 0.05);
      
      &::after {
        transform: scaleX(1);
        transform-origin: left;
      }
    }
  }
}



