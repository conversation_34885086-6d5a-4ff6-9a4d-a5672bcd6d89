.simple-chat {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: system-ui, sans-serif;

  &__modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  &__modal {
    background-color: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;

    h2 {
      margin-bottom: 1rem;
      text-align: center;
      color: #333;
    }

    p {
      margin-bottom: 1.5rem;
      text-align: center;
      color: #666;
    }
  }

  &__container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }

  &__header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h1 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 2rem;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 1.125rem;
    }
  }

  &__call-status {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border-radius: 20px;
    font-weight: 600;
    display: inline-block;
  }

  &__room {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  &__users {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.25rem;
    }
  }

  &__user-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  &__user {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
  }

  &__user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1rem;
  }

  &__user-info {
    flex: 1;
  }

  &__username {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 0.25rem;
  }

  &__user-status {
    font-size: 0.875rem;
    color: #666;

    span {
      margin-right: 0.5rem;
    }
  }

  &__voice-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;

    h3 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.25rem;
    }

    p {
      margin-bottom: 1.5rem;
      color: #666;
      line-height: 1.5;
    }
  }

  &__voice-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  &__voice-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 150px;

    &.join {
      background-color: #28a745;
      color: white;

      &:hover {
        background-color: #218838;
        transform: translateY(-2px);
      }
    }

    &.muted {
      background-color: #ffc107;
      color: #333;

      &:hover {
        background-color: #e0a800;
        transform: translateY(-2px);
      }
    }

    &:not(.muted):not(.join):not(.leave) {
      background-color: #007bff;
      color: white;

      &:hover {
        background-color: #0056b3;
        transform: translateY(-2px);
      }
    }

    &.leave {
      background-color: #dc3545;
      color: white;

      &:hover {
        background-color: #c82333;
        transform: translateY(-2px);
      }
    }
  }

  &__footer {
    text-align: center;
  }

  &__back-btn {
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: 2px solid #ddd;
    border-radius: 8px;
    color: #666;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;

    &:hover {
      border-color: #007bff;
      color: #007bff;
      transform: translateY(-1px);
    }
  }

  &__error {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background-color: #dc3545;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;

    p {
      margin: 0 0 1rem 0;
    }

    button {
      background-color: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 600;

      &:hover {
        background-color: rgba(255, 255, 255, 0.3);
      }
    }
  }
}
