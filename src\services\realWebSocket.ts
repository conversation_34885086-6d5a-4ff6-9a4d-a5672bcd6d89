// Real WebSocket service for internet-wide voice chat

export enum MessageType {
  USER_JOIN = 'user_join',
  USER_LEAVE = 'user_leave',
  USER_LIST = 'user_list',
  CALL_START = 'call_start',
  CALL_END = 'call_end',
  CALL_JOIN = 'call_join',
  CALL_LEAVE = 'call_leave',
  OFFER = 'offer',
  ANSWER = 'answer',
  ICE_CANDIDATE = 'ice_candidate',
  ERROR = 'error',
  HEARTBEAT = 'heartbeat'
}

export interface ChatUser {
  id: string;
  username: string;
  isAdmin: boolean;
  isInCall: boolean;
  isMuted: boolean;
  joinedAt: Date;
}

export interface WebSocketMessage {
  type: MessageType;
  payload: any;
  timestamp: string;
  roomId?: string;
}

export class RealWebSocketService {
  private ws: WebSocket | null = null;
  private listeners: Map<MessageType, Set<Function>> = new Map();
  private isConnected = false;
  private roomId: string;
  private userId: string;
  private username: string;
  private currentUser: ChatUser | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private serverUrl: string;

  constructor(roomId: string, userId: string, username: string) {
    this.roomId = roomId;
    this.userId = userId;
    this.username = username;
    
    // Use environment variable or default to localhost
    this.serverUrl = import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:3001';
    
    this.initializeListeners();
  }

  private initializeListeners() {
    Object.values(MessageType).forEach(type => {
      this.listeners.set(type, new Set());
    });
  }

  // Connect to the WebSocket server
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        console.log(`Connecting to WebSocket server: ${this.serverUrl}`);
        this.ws = new WebSocket(this.serverUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected successfully');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          // Create current user
          this.currentUser = {
            id: this.userId,
            username: this.username,
            isAdmin: false,
            isInCall: false,
            isMuted: false,
            joinedAt: new Date()
          };

          // Join the room
          this.send(MessageType.USER_JOIN, this.currentUser);
          
          // Start heartbeat
          this.startHeartbeat();
          
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            console.log('Received message:', message.type, message.payload);
            this.handleMessage(message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket connection closed:', event.code, event.reason);
          this.isConnected = false;
          this.stopHeartbeat();
          
          // Attempt to reconnect if not a clean close
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          reject(new Error('Failed to connect to WebSocket server'));
        };

      } catch (error) {
        console.error('Error creating WebSocket connection:', error);
        reject(error);
      }
    });
  }

  private attemptReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          this.emit(MessageType.ERROR, { 
            message: 'Failed to reconnect to server after multiple attempts' 
          });
        }
      });
    }, delay);
  }

  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send(MessageType.HEARTBEAT, {});
      }
    }, 30000); // Send heartbeat every 30 seconds
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // Disconnect from WebSocket server
  disconnect() {
    this.stopHeartbeat();
    
    if (this.isConnected && this.currentUser) {
      this.send(MessageType.USER_LEAVE, { 
        userId: this.userId, 
        username: this.username 
      });
    }
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnecting');
      this.ws = null;
    }
    
    this.isConnected = false;
  }

  // Send message to WebSocket server
  send(type: MessageType, payload: any) {
    if (this.isConnected && this.ws) {
      const message: WebSocketMessage = {
        type,
        payload,
        timestamp: new Date().toISOString(),
        roomId: this.roomId
      };
      
      console.log('Sending message:', type, payload);
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('Cannot send message: WebSocket not connected');
    }
  }

  // Handle incoming messages
  private handleMessage(message: WebSocketMessage) {
    // Emit to listeners
    this.emit(message.type, message.payload);
  }

  // Add event listener
  on(type: MessageType, callback: Function) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.add(callback);
    }
  }

  // Remove event listener
  off(type: MessageType, callback: Function) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  // Emit event to listeners
  private emit(type: MessageType, payload: any) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(payload);
        } catch (error) {
          console.error('Error in message listener:', error);
        }
      });
    }
  }

  // Voice call methods
  startCall() {
    this.send(MessageType.CALL_START, { userId: this.userId });
  }

  endCall() {
    this.send(MessageType.CALL_END, { userId: this.userId });
  }

  joinCall() {
    this.send(MessageType.CALL_JOIN, { userId: this.userId });
  }

  leaveCall() {
    this.send(MessageType.CALL_LEAVE, { userId: this.userId });
  }

  // WebRTC signaling methods
  sendOffer(targetUserId: string, offer: RTCSessionDescriptionInit) {
    this.send(MessageType.OFFER, {
      targetUserId,
      offer,
      fromUserId: this.userId
    });
  }

  sendAnswer(targetUserId: string, answer: RTCSessionDescriptionInit) {
    this.send(MessageType.ANSWER, {
      targetUserId,
      answer,
      fromUserId: this.userId
    });
  }

  sendIceCandidate(targetUserId: string, candidate: RTCIceCandidate) {
    this.send(MessageType.ICE_CANDIDATE, {
      targetUserId,
      candidate,
      fromUserId: this.userId
    });
  }

  // Get connection status
  get isConnectedStatus(): boolean {
    return this.isConnected;
  }

  // Update user call status
  updateUserCallStatus(isInCall: boolean, isMuted: boolean = false) {
    if (this.currentUser) {
      this.currentUser.isInCall = isInCall;
      this.currentUser.isMuted = isMuted;
      
      // Send updated user info to server
      this.send(MessageType.USER_JOIN, this.currentUser);
      
      console.log('Updated user call status:', {
        userId: this.userId,
        isInCall,
        isMuted
      });
    }
  }
}

export default RealWebSocketService;
