// Import Sass color module at the top of the file
@use "sass:color";

// Colors (original variables)
$color-mythlight: #D0DBEE;
$color-storm-mist: #A1B1CC;
$color-twilight-ash: #848B9B;
$color-eclipse-black: #272A35;
$color-oracle-blue: #0E73C0;

// Additional theme palettes
$theme-wolzyn-light: (
  primary: #D0DBEE,
  secondary: #A1B1CC,
  tertiary: #C7D2E5,
  text: #272A35,
  accent: #0E73C0
);

$theme-wolzyn-dark: (
  primary: #272A35,
  secondary: #3C4457,
  tertiary: #5B6375,
  text: #D0DBEE,
  accent: #0E73C0
);

$theme-rebellion: (
  primary: #1A1D24,
  secondary: #3B0A0A,
  tertiary: #5C1E1E,
  text: #F2F2F2,
  accent: #D72638
);

$theme-ascension: (
  primary: #F5F9FF,
  secondary: #E0ECF8,
  tertiary: #C9D7F0,
  text: #1E2A38,
  accent: #4A90E2
);

$theme-duality: (
  primary: #FDFDFD,
  secondary: #D9D9D9,
  tertiary: #B0B0B0,
  text: #0F0F0F,
  accent: #7F00FF
);

$theme-ambitious: (
  primary: #F0F3F5,
  // Alabaster - light background
  secondary: #B4C5E4,
  // Powder Blue - primary action
  tertiary: #D4A373,
  // Burnt Sienna - accent
  text: #2D3142,
  // Gunmetal - primary text
  accent: #D4A373 // Burnt Sienna - accent
);

// Add the Forged Ember theme
$theme-ember: (
  primary: #E4E3E0,
  // Bone White - light background
  secondary: #6F6F6F,
  // Flint Grey - secondary elements
  tertiary: #2A363B,
  // Forest Night - tertiary elements
  text: #1C1C1E,
  // Onyx - primary text
  accent: #BF5700 // Forged Ember - accent color
);

// Add the Cayenne Spice theme
$theme-spice: (
  primary: #E4E3E0,
  // Bone White - light background
  secondary: #A9A9A9,
  // Ash Grey - secondary elements
  tertiary: #D1B499,
  // Desert Sand - tertiary elements
  text: #2C3E50,
  // Deep Indigo - primary text
  accent: #D95D39 // Cayenne Spice - accent color
);

// Typography
$font-primary: 'DM Sans', sans-serif;
$font-secondary: 'Inter', sans-serif;
$font-serif: 'Playfair Display', serif;
$font-editorial: 'Libre Caslon Text', serif;
$font-logo: 'DM Serif Text', serif;
$font-mono: 'Roboto Mono', monospace;

// Spacing
$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 2rem;
$spacing-xl: 4rem;
$spacing-xxl: 6rem; // Adding the missing xxl spacing variable

// Breakpoints
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-modal: 1030;
$z-index-tooltip: 1040;

// Add this to your variables
:root {
  // Light theme (default)
  --bg-primary: #ffffff;
  --bg-primary-rgb: 255, 255, 255;
  --bg-secondary: #f8f9fa;
  --text-primary: #212529;
  --text-secondary: #495057;
  --accent-primary: #3a86ff;
  --accent-primary-rgb: 58, 134, 255;
  --accent-secondary: #8338ec;
  --accent-secondary-rgb: 131, 56, 236;
  --border-color: #dee2e6;
  --logo-icon-color: #3a86ff;
  --logo-text-color: #212529;
}

// Dark theme
.dark-theme {
  --bg-primary: #121212;
  --bg-primary-rgb: 18, 18, 18;
  --bg-secondary: #1e1e1e;
  --text-primary: #f8f9fa;
  --text-secondary: #adb5bd;
  --accent-primary: #3a86ff;
  --accent-primary-rgb: 58, 134, 255;
  --accent-secondary: #8338ec;
  --accent-secondary-rgb: 131, 56, 236;
  --border-color: #343a40;
  --logo-icon-color: #3a86ff;
  --logo-text-color: #f8f9fa;
}