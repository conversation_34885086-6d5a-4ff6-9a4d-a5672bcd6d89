@use '../styles/variables' as *;
@use '../styles/mixins' as *;
@use '../styles/common';

.coming-soon-page {
  min-height: calc(100vh - 160px); // Adjust based on header/footer height
  @include flex-center;
  padding: $spacing-xl 0;
  background-color: var(--bg-primary);
}

.coming-soon {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
  padding: $spacing-xl;
  border-radius: 8px;
  background-color: var(--bg-secondary);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  @include fade-in(0.5s);
  
  &__icon {
    font-size: 3rem;
    margin-bottom: $spacing-lg;
    color: var(--accent-primary);
    @include slide-in('down', 30px, 0.6s);
    
    .icon-studio {
      display: inline-block;
    }
  }
  
  &__title {
    @include heading-1;
    margin-bottom: $spacing-md;
    color: var(--text-primary);
    @include slide-in('up', 20px, 0.7s);
  }
  
  &__message {
    @include body-text;
    margin-bottom: $spacing-xl;
    color: var(--text-secondary);
    @include slide-in('up', 15px, 0.8s);
  }
  
  &__countdown {
    display: flex;
    justify-content: center;
    gap: $spacing-md;
    margin-bottom: $spacing-xl;
    @include slide-in('up', 10px, 0.9s);
    
    &-item {
      @include flex-column;
      align-items: center;
      min-width: 80px;
    }
    
    &-value {
      font-size: 2rem;
      font-weight: 700;
      color: var(--accent-primary);
    }
    
    &-label {
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 1px;
      color: var(--text-secondary);
    }
  }
  
  &__actions {
    @include flex-center;
    gap: $spacing-md;
    @include slide-in('up', 5px, 1s);
    
    .btn {
      min-width: 120px;
    }
  }
  
  @include mobile {
    padding: $spacing-lg $spacing-md;
    
    &__countdown {
      gap: $spacing-sm;
      
      &-item {
        min-width: 60px;
      }
      
      &-value {
        font-size: 1.5rem;
      }
      
      &-label {
        font-size: 0.8rem;
      }
    }
    
    &__actions {
      flex-direction: column;
      gap: $spacing-sm;
      
      .btn {
        width: 100%;
      }
    }
  }
}


