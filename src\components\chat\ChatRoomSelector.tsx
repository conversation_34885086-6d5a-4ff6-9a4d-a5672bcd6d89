import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import './ChatRoomSelector.scss';

interface ChatRoom {
  id: string;
  name: string;
  description: string;
  userCount: number;
  isActive: boolean;
}

const defaultRooms: ChatRoom[] = [
  {
    id: 'general',
    name: 'General',
    description: 'General discussion for everyone',
    userCount: 12,
    isActive: true
  },
  {
    id: 'fashion',
    name: 'Fashion Talk',
    description: 'Discuss latest fashion trends',
    userCount: 8,
    isActive: true
  },
  {
    id: 'support',
    name: 'Customer Support',
    description: 'Get help with your orders',
    userCount: 3,
    isActive: true
  },
  {
    id: 'vip',
    name: 'VIP Lounge',
    description: 'Exclusive chat for VIP members',
    userCount: 5,
    isActive: true
  }
];

const ChatRoomSelector: React.FC = () => {
  const navigate = useNavigate();
  const [customRoom, setCustomRoom] = useState('');
  const [rooms] = useState<ChatRoom[]>(defaultRooms);

  const handleJoinRoom = (roomId: string) => {
    navigate(`/chat?room=${encodeURIComponent(roomId)}`);
  };

  const handleJoinCustomRoom = (e: React.FormEvent) => {
    e.preventDefault();
    if (customRoom.trim()) {
      const roomId = customRoom.trim().toLowerCase().replace(/[^a-z0-9-_]/g, '-');
      navigate(`/chat?room=${encodeURIComponent(roomId)}`);
    }
  };

  return (
    <div className="chat-room-selector">
      <div className="container">
        <motion.div
          className="chat-room-selector__content"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="chat-room-selector__header">
            <h1>Join a Chat Room</h1>
            <p>Connect with other Wolzyn Apparels community members</p>
          </div>

          {/* Default Rooms */}
          <div className="chat-room-selector__rooms">
            <h2>Available Rooms</h2>
            <div className="chat-room-selector__room-grid">
              {rooms.map((room, index) => (
                <motion.div
                  key={room.id}
                  className="chat-room-selector__room-card"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  onClick={() => handleJoinRoom(room.id)}
                >
                  <div className="chat-room-selector__room-header">
                    <h3>#{room.name}</h3>
                    <div className="chat-room-selector__room-status">
                      <span className={`chat-room-selector__status-dot ${room.isActive ? 'active' : 'inactive'}`} />
                      <span className="chat-room-selector__user-count">
                        {room.userCount} online
                      </span>
                    </div>
                  </div>
                  <p className="chat-room-selector__room-description">
                    {room.description}
                  </p>
                  <button className="chat-room-selector__join-btn">
                    Join Room
                  </button>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Custom Room */}
          <div className="chat-room-selector__custom">
            <h2>Create or Join Custom Room</h2>
            <form onSubmit={handleJoinCustomRoom} className="chat-room-selector__custom-form">
              <div className="chat-room-selector__input-group">
                <input
                  type="text"
                  value={customRoom}
                  onChange={(e) => setCustomRoom(e.target.value)}
                  placeholder="Enter room name..."
                  className="chat-room-selector__custom-input"
                  maxLength={30}
                />
                <button
                  type="submit"
                  disabled={!customRoom.trim()}
                  className="chat-room-selector__custom-btn"
                >
                  Join
                </button>
              </div>
              <p className="chat-room-selector__custom-note">
                Room names will be automatically formatted (lowercase, no special characters)
              </p>
            </form>
          </div>

          {/* Features */}
          <div className="chat-room-selector__features">
            <h2>Chat Features</h2>
            <div className="chat-room-selector__feature-grid">
              <div className="chat-room-selector__feature">
                <div className="chat-room-selector__feature-icon">💬</div>
                <h4>Real-time Chat</h4>
                <p>Instant messaging with typing indicators</p>
              </div>
              <div className="chat-room-selector__feature">
                <div className="chat-room-selector__feature-icon">🎤</div>
                <h4>Voice Calls</h4>
                <p>High-quality voice communication</p>
              </div>
              <div className="chat-room-selector__feature">
                <div className="chat-room-selector__feature-icon">👥</div>
                <h4>User Management</h4>
                <p>See who's online and manage users</p>
              </div>
              <div className="chat-room-selector__feature">
                <div className="chat-room-selector__feature-icon">🔒</div>
                <h4>Secure</h4>
                <p>End-to-end encrypted communications</p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="chat-room-selector__quick-actions">
            <button
              onClick={() => handleJoinRoom('general')}
              className="chat-room-selector__quick-btn chat-room-selector__quick-btn--primary"
            >
              Quick Join General
            </button>
            <button
              onClick={() => navigate('/')}
              className="chat-room-selector__quick-btn chat-room-selector__quick-btn--secondary"
            >
              Back to Home
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ChatRoomSelector;
