# 🥽 VR Video Viewer

A simple web-based VR video viewer that allows users to view 360° videos in immersive VR mode using A-Frame.

## ✨ Features

- 🎬 **URL-based Video Loading** - Load any video via URL
- 🥽 **VR Mode** - Immersive 360° video viewing
- 📱 **Cross-platform** - Works on desktop, mobile, and VR headsets
- 🎮 **Look Controls** - Mouse/touch to look around
- 🔄 **Easy Toggle** - Switch between regular and VR mode
- 📺 **Sample Videos** - Pre-loaded sample videos for testing

## 🚀 How to Use

### Basic Usage
1. Navigate to `/vr-viewer`
2. Enter a video URL or select a sample video
3. Click "Load Video" to preview
4. Click "Enter VR Mode" for immersive viewing
5. Use mouse/touch to look around in VR mode
6. Click "Exit VR" or press ESC to return

### Supported Video Formats
- MP4
- WebM
- OGV
- Any HTML5 video format

### VR Device Support
- **Desktop**: Mouse look controls
- **Mobile**: Touch and gyroscope controls
- **VR Headsets**: 
  - Oculus Quest/Rift
  - HTC Vive
  - Google Cardboard
  - Samsung Gear VR
  - Any WebXR compatible device

## 🔧 Technical Implementation

### Core Technologies
- **A-Frame 1.4.0** - WebVR/WebXR framework
- **React** - Component framework
- **WebXR** - VR device support
- **HTML5 Video** - Video playback

### Key Components

#### VRViewer Component (`src/pages/VRViewer.tsx`)
- URL input and validation
- Video loading and error handling
- VR mode toggle
- A-Frame scene management

#### A-Frame Scene Structure
```html
<a-scene embedded vr-mode-ui="enabled: true">
  <a-assets>
    <video id="vr-video" src="..." />
  </a-assets>
  <a-videosphere src="#vr-video" />
  <a-camera look-controls />
</a-scene>
```

## 📁 File Structure

```
src/
├── pages/
│   ├── VRViewer.tsx         # Main VR viewer component
│   └── VRViewer.scss        # Styling
├── types/
│   └── aframe.d.ts          # A-Frame TypeScript declarations
└── components/header/
    └── Header.jsx           # Navigation (includes VR link)
```

## 🌐 Browser Requirements

### Minimum Requirements
- Modern browser with WebGL support
- JavaScript enabled
- HTTPS (for VR features)

### Optimal Experience
- Chrome 79+ or Firefox 70+
- VR headset (optional)
- Fast internet connection for video streaming

## 🎯 Routes

- **`/vr-viewer`** - Main VR video viewer interface

## 🔧 Configuration

### A-Frame CDN
The component dynamically loads A-Frame from CDN:
```javascript
script.src = 'https://aframe.io/releases/1.4.0/aframe.min.js';
```

### Video Sources
Videos must be:
- Publicly accessible URLs
- CORS-enabled for cross-origin requests
- HTML5 compatible formats

## 🚨 Limitations

### Current Limitations
- **CORS Restrictions** - Videos must allow cross-origin access
- **No Local Files** - Cannot load local video files directly
- **Basic Controls** - Limited video controls in VR mode
- **Single Video** - One video at a time

### Performance Considerations
- Large video files may take time to load
- VR mode requires more processing power
- Mobile devices may have limited performance

## 🔍 Troubleshooting

### Common Issues

1. **Video Won't Load**
   - Check if URL is accessible
   - Verify CORS headers on video server
   - Try a different video format

2. **VR Mode Not Working**
   - Ensure HTTPS connection
   - Check browser VR support
   - Try refreshing the page

3. **Poor Performance**
   - Use smaller video files
   - Close other browser tabs
   - Check internet connection speed

### Browser Console
Check browser console for detailed error messages:
```javascript
// A-Frame logs
console.log('A-Frame loaded:', window.AFRAME);

// Video errors
video.addEventListener('error', (e) => {
  console.error('Video error:', e);
});
```

## 🎉 Success!

You now have a fully functional VR video viewer that can:

- ✅ Load videos from any URL
- ✅ Display in immersive VR mode
- ✅ Work across devices and platforms
- ✅ Provide smooth look controls
- ✅ Handle errors gracefully

Perfect for:
- **360° Video Viewing**
- **VR Content Demos**
- **Immersive Experiences**
- **Educational Content**

---

**🥽 Ready to explore VR? Visit `/vr-viewer` and start watching!**
