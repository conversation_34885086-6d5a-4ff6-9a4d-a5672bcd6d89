@use '../styles/variables' as *;
@use '../styles/mixins' as *;

.chat-page {
  height: 100vh;
  background-color: var(--bg-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // Modal overlay
  &__modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  &__modal {
    background-color: var(--bg-secondary);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;

    h2 {
      margin-bottom: 1rem;
      color: var(--text-primary);
      text-align: center;
    }

    p {
      margin-bottom: 1.5rem;
      color: var(--text-secondary);
      text-align: center;
    }

    input {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid var(--border-color);
      border-radius: 8px;
      background-color: var(--bg-primary);
      color: var(--text-primary);
      font-size: 1rem;
      margin-bottom: 1.5rem;

      &:focus {
        outline: none;
        border-color: var(--accent-primary);
      }
    }
  }

  &__modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;

    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 6px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      &-primary {
        background-color: var(--accent-primary);
        color: white;

        &:hover:not(:disabled) {
          background-color: var(--accent-secondary);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }

      &-secondary {
        background-color: transparent;
        color: var(--text-secondary);
        border: 1px solid var(--border-color);

        &:hover {
          background-color: var(--bg-primary);
        }
      }
    }
  }

  // Main container
  &__container {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  // Header
  &__header {
    background-color: var(--bg-secondary);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;

    @media (max-width: $breakpoint-md) {
      padding: 0.75rem 1rem;
      flex-direction: column;
      gap: 1rem;
    }
  }

  &__room-info {
    h1 {
      margin: 0;
      color: var(--text-primary);
      font-size: 1.5rem;
      font-weight: 700;

      @media (max-width: $breakpoint-md) {
        font-size: 1.25rem;
      }
    }
  }

  &__user-count {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-left: 1rem;

    @media (max-width: $breakpoint-md) {
      margin-left: 0;
      display: block;
    }
  }

  // Call controls
  &__call-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  &__call-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  &__call-indicator {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    animation: pulse 2s infinite;
  }

  &__call-buttons {
    display: flex;
    gap: 0.5rem;
  }

  &__call-btn {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;

    &--start {
      background-color: #10b981;

      &:hover {
        background-color: #059669;
        transform: scale(1.05);
      }
    }

    &--end {
      background-color: #ef4444;

      &:hover {
        background-color: #dc2626;
        transform: scale(1.05);
      }
    }

    &--muted {
      background-color: #f59e0b;

      &:hover {
        background-color: #d97706;
        transform: scale(1.05);
      }
    }

    &:not(&--muted) {
      background-color: var(--accent-primary);

      &:hover {
        background-color: var(--accent-secondary);
        transform: scale(1.05);
      }
    }
  }

  // Content area
  &__content {
    flex: 1;
    display: flex;
    overflow: hidden;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
    }
  }

  // Sidebar
  &__sidebar {
    width: 250px;
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
    overflow-y: auto;
    flex-shrink: 0;

    @media (max-width: $breakpoint-md) {
      width: 100%;
      max-height: 200px;
      border-right: none;
      border-bottom: 1px solid var(--border-color);
    }

    h3 {
      margin: 0 0 1rem 0;
      color: var(--text-primary);
      font-size: 1rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }

  &__user-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    @media (max-width: $breakpoint-md) {
      flex-direction: row;
      flex-wrap: wrap;
      gap: 0.75rem;
    }
  }

  &__user-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background-color 0.2s ease;
    position: relative;

    &:hover {
      background-color: var(--bg-primary);
    }

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      text-align: center;
      padding: 0.75rem;
      min-width: 80px;
    }
  }

  &__user-info {
    flex: 1;
    min-width: 0;

    @media (max-width: $breakpoint-md) {
      flex: none;
    }
  }

  &__username {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    @media (max-width: $breakpoint-md) {
      font-size: 0.75rem;
      white-space: normal;
    }
  }

  &__admin-badge {
    background-color: var(--accent-primary);
    color: white;
    font-size: 0.625rem;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    margin-left: 0.5rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  &__user-status {
    display: flex;
    gap: 0.25rem;
    margin-top: 0.125rem;

    @media (max-width: $breakpoint-md) {
      justify-content: center;
      margin-top: 0.25rem;
    }
  }

  &__in-call,
  &__muted {
    font-size: 0.75rem;
  }

  &__kick-btn {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    opacity: 0;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: bold;

    .chat-page__user-item:hover & {
      opacity: 1;
    }

    &:hover {
      background-color: rgba(239, 68, 68, 0.1);
    }

    @media (max-width: $breakpoint-md) {
      position: absolute;
      top: 0.25rem;
      right: 0.25rem;
      opacity: 1;
    }
  }

  // Main chat area
  &__main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &__messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;

    @media (max-width: $breakpoint-md) {
      padding: 0.75rem;
      gap: 0.75rem;
    }
  }

  &__message {
    display: flex;
    gap: 0.75rem;
    max-width: 70%;
    animation: slideIn 0.3s ease-out;

    &--own {
      align-self: flex-end;
      flex-direction: row-reverse;

      .chat-page__message-content {
        background-color: var(--accent-primary);
        color: white;
      }

      .chat-page__message-username {
        color: rgba(255, 255, 255, 0.9);
      }

      .chat-page__message-time {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    &--system {
      align-self: center;
      max-width: none;

      .chat-page__message-content {
        background-color: rgba(var(--accent-primary-rgb), 0.1);
        color: var(--accent-primary);
        text-align: center;
        font-style: italic;
        font-size: 0.875rem;
      }
    }

    @media (max-width: $breakpoint-md) {
      max-width: 85%;
    }
  }

  &__message-avatar {
    flex-shrink: 0;
    margin-top: 0.25rem;
  }

  &__message-content {
    background-color: var(--bg-secondary);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    min-width: 0;
  }

  &__message-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
  }

  &__message-username {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
  }

  &__message-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
  }

  &__message-text {
    color: var(--text-primary);
    line-height: 1.4;
    word-wrap: break-word;
  }

  &__typing {
    padding: 0.5rem 1rem;
    color: var(--text-secondary);
    font-style: italic;
    font-size: 0.875rem;
    animation: fadeIn 0.3s ease-in;
  }

  // Message input
  &__input-form {
    display: flex;
    gap: 0.75rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);

    @media (max-width: $breakpoint-md) {
      padding: 0.75rem;
      gap: 0.5rem;
    }
  }

  &__message-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 24px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    resize: none;
    outline: none;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: var(--accent-primary);
    }

    &::placeholder {
      color: var(--text-secondary);
    }
  }

  &__send-btn {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    border: none;
    background-color: var(--accent-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;

    &:hover:not(:disabled) {
      background-color: var(--accent-secondary);
      transform: scale(1.05);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
  }

  // Error state
  &__error {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background-color: #ef4444;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    animation: slideInUp 0.3s ease-out;

    p {
      margin: 0 0 1rem 0;
    }

    button {
      background-color: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 600;

      &:hover {
        background-color: rgba(255, 255, 255, 0.3);
      }
    }
  }
}

// Animations
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}