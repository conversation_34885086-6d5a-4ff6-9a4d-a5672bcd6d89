// Variables
$font-primary: 'D<PERSON> Sans', sans-serif;
$font-serif: 'Playfair Display', serif;
$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;
$spacing-xxl: 3rem;
$tile-gap: 2rem;
$brand-accent: #0E73C0;

// Mixins
@mixin mobile {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1024px) {
    @content;
  }
}

.storytelling-product {
  padding: $spacing-xl 0 $spacing-xxl;
  background-color: var(--bg-primary, #ffffff);
  color: var(--text-primary, #000000);
  
  // Loading state
  &--loading {
    height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__loading {
    text-align: center;
    
    &-spinner {
      width: 40px;
      height: 40px;
      margin: 0 auto $spacing-md;
      border: 3px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top-color: $brand-accent;
      animation: spin 1s ease-in-out infinite;
    }
    
    p {
      font-family: $font-primary;
      font-size: 1rem;
      color: var(--text-secondary, #666);
    }
  }
  
  // Not found state
  &--not-found {
    height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__not-found {
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
    padding: $spacing-xl;
    
    h2 {
      font-family: $font-serif;
      font-size: 2rem;
      margin-bottom: $spacing-md;
    }
    
    p {
      font-family: $font-primary;
      font-size: 1rem;
      color: var(--text-secondary, #666);
      margin-bottom: $spacing-lg;
    }
    
    &-actions {
      display: flex;
      gap: $spacing-md;
      justify-content: center;
      
      .btn {
        font-family: $font-primary;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        padding: 0.75rem 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &-primary {
          background-color: $brand-accent;
          color: var(--text-inverse, #fff);
          border: none;
          
          &:hover {
            background-color: darken($brand-accent, 10%);
          }
        }
        
        &-secondary {
          background-color: transparent;
          color: var(--text-primary, #000);
          border: 1px solid var(--text-primary, #000);
          
          &:hover {
            background-color: var(--bg-secondary, #f5f5f5);
          }
        }
      }
    }
  }
  
  &__breadcrumb {
    padding: 0 $spacing-lg;
    margin-bottom: $spacing-xl;
    font-family: $font-primary;
    font-size: 0.875rem;
    
    a {
      color: var(--text-secondary, #666);
      text-decoration: none;
      transition: color 0.3s ease;
      
      &:hover {
        color: $brand-accent;
      }
    }
    
    span {
      color: var(--text-primary, #000);
    }
    
    @include desktop {
      padding: 0 $spacing-xl;
      max-width: 1200px;
      margin-left: auto;
      margin-right: auto;
    }
  }
  
  &__container {
    max-width: 1300px;
    margin: 0 auto;
    padding: 0 $spacing-lg;
    
    @include desktop {
      padding: 0 $spacing-xl;
    }
  }
  
  &__grid-top,
  &__grid-bottom {
    display: grid;
    grid-template-columns: 1fr;
    gap: $tile-gap;
    max-width: 1024px;
    margin: 0 auto;
    
    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include desktop {
      grid-template-columns: repeat(2, 1fr);
      gap: $spacing-xxl;
    }
  }
  
  &__grid-top {
    margin-bottom: $spacing-xl;
  }
  
  &__grid-bottom {
    margin-top: $spacing-xl;
  }
  
  &__tile {
    background-color: var(--bg-secondary, #f9f9f9);
    padding: $spacing-xl;
    position: relative;
    overflow: hidden;
    border-radius: 4px;
    cursor: pointer;
    height: 100%;
    transform-origin: center;
    
    &--image {
      padding: 0;
      grid-area: image;
      aspect-ratio: 1/1;
    }
    
    &--story {
      grid-area: story;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
    }
    
    &--outcome {
      grid-area: outcome;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    
    &--compliment {
      grid-area: compliment;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--accent-secondary, #f0f0f0);
    }
  }
  
  &__image-container {
    width: 100%;
    height: 100%;
    position: relative;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }
  }
  
  &__badge {
    position: absolute;
    top: $spacing-md;
    right: $spacing-md;
    background-color: $brand-accent;
    color: var(--text-inverse, #fff);
    font-family: $font-primary;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    padding: 0.25rem 0.5rem;
    border-radius: 2px;
  }
  
  &__collection {
    font-family: $font-primary;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--text-secondary, #666);
    margin-bottom: $spacing-sm;
  }
  
  &__name {
    font-family: $font-serif;
    font-size: 2rem;
    font-weight: 500;
    margin: 0 0 $spacing-sm;
    line-height: 1.2;
    display: inline-block;
    
    @include desktop {
      font-size: 2.5rem;
    }
  }
  
  &__price {
    font-family: $font-primary;
    font-size: 1.25rem;
    margin-bottom: $spacing-lg;
    color: $brand-accent;
  }
  
  &__story-text {
    font-family: $font-serif;
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--text-primary, #000);
    margin-bottom: $spacing-lg;
    
    p {
      margin: 0 0 $spacing-md;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  &__color-selector {
    margin-bottom: $spacing-lg;
  }
  
  &__size-selector {
    margin-bottom: $spacing-lg;
  }
  
  &__size-guide-btn {
    background: none;
    border: none;
    color: var(--text-secondary, #666);
    font-size: 0.875rem;
    text-decoration: underline;
    cursor: pointer;
    transition: color 0.3s ease;
    padding: 0;
    margin-left: $spacing-md;
    
    &:hover {
      color: $brand-accent;
    }
  }
  
  &__size-guide-icon {
    display: inline-flex;
    margin-right: $spacing-xs;
  }
  
  &__outcome-title {
    font-family: $font-serif;
    font-size: 1.5rem;
    font-weight: 500;
    margin: 0 0 $spacing-md;
  }
  
  &__keywords {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-sm;
    margin-bottom: $spacing-lg;
  }
  
  &__keyword {
    font-family: $font-primary;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    padding: 0.25rem 0.75rem;
    background-color: var(--bg-primary, #fff);
    color: var(--text-primary, #000);
    border-radius: 2px;
  }
  
  &__details {
    margin-top: $spacing-lg;
  }
  
  &__detail {
    margin-bottom: $spacing-md;
    
    h3 {
      font-family: $font-serif;
      font-size: 1.125rem;
      font-weight: 500;
      margin-bottom: $spacing-sm;
    }
    
    p {
      font-family: $font-primary;
      font-size: 0.875rem;
      line-height: 1.6;
      color: var(--text-secondary, #666);
    }
  }
  
  &__add-to-cart {
    font-family: $font-primary;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    background-color: $brand-accent;
    color: var(--text-inverse, #fff);
    border: none;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 2px;
    
    &:hover {
      background-color: darken($brand-accent, 10%);
      transform: translateY(-2px);
    }
    
    &--disabled {
      background-color: var(--text-secondary, #666);
      opacity: 0.7;
      cursor: not-allowed;
      
      &:hover {
        transform: none;
      }
    }
  }
  
  &__compliment-container {
    text-align: center;
  }
  
  &__symbol {
    font-size: 2.5rem;
    margin-bottom: $spacing-md;
    color: $brand-accent;
    
    @include desktop {
      font-size: 3rem;
    }
  }
  
  &__compliment-text {
    font-family: $font-serif;
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: $spacing-md;
  }
  
  &__drop-theme {
    font-family: $font-primary;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--text-secondary, #666);
  }
  
  &__selection-section {
    margin: $spacing-xl 0;
    background-color: var(--bg-secondary, #f5f5f5);
    border-radius: 8px;
    padding: $spacing-lg;
    max-width: 1024px;
    margin-left: auto;
    margin-right: auto;
  }
  
  &__selection-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    gap: $spacing-lg;
    
    @include desktop {
      justify-content: space-between;
    }
  }
  
  &__color-selector,
  &__size-selector {
    display: flex;
    align-items: center;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Dark mode adjustments
:root[data-theme="dark"] {
  .storytelling-product {
    &__tile {
      background-color: var(--bg-secondary, #1a1a1a);
    }
    
    &__keyword {
      background-color: var(--bg-primary, #121212);
    }
  }
}

// Mobile-specific animations
@include mobile {
  .storytelling-product {
    &__grid {
      max-width: 100%;
    }
    
    &__tile {
      opacity: 0;
      transform: translateY(50px);
      transition: opacity 0.6s ease, transform 0.6s ease;
      
      &.in-view {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
}



