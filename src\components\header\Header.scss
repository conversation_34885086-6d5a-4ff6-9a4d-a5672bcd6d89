@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  transition: transform 0.3s ease, background-color 0.3s ease;
  
  &__container {
    display: flex;
    align-items: center; // Change to center alignment
    justify-content: space-between; // Add space between for better positioning
    max-width: 1280px;
    margin: 0 auto;
    padding: 1rem 1.5rem; // Add consistent padding
    position: relative; // Maintain position relative
    height: 80px; // Set a consistent height
    
    @media (max-width: 768px) {
      height: 70px; // Slightly smaller on mobile
    }
  }

  &__user-container {
    position: absolute;
    top: 0;
    right: 1.5rem;
    z-index: 10;
    display: flex;
    align-items: center;
    height: 100%;
  }

  &__logo-container {
    margin-bottom: 1.5rem;
  }

  &__logo {
    display: block;
    text-decoration: none;

    &-text {
      font-family: $font-serif;
      font-size: 2rem;
      color: var(--text-primary);
      letter-spacing: 0.05em;
    }

    .icon-studio {
      font-size: 2rem;
      margin-right: $spacing-xs;
      color: var(--logo-icon-color);
    }
  }
}

// Desktop Navigation
.desktop-nav {
  display: none;

  @include tablet {
    display: block;
  }

  &__list {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
  }

  &__item {
    margin: 0 1.5rem;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    &--user {
      margin-left: 1rem;
    }
  }

  &__link {
    position: relative;
    font-family: $font-primary;
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.5rem 0;
    transition: color 0.3s ease;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background-color: var(--text-primary);
      transform: scaleX(0);
      transform-origin: right;
      transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1);
    }

    &:hover {
      &::after {
        transform: scaleX(1);
        transform-origin: left;
      }
    }
  }
}

// Mobile Navigation Trigger
.mobile-nav-trigger {
  display: block;
  cursor: pointer;
  padding: 0.5rem;
  position: relative; // Add position relative
  z-index: 10; // Ensure it's above other elements
  margin-left: 1rem; // Add some spacing

  @include tablet {
    display: none;
  }

  &__text {
    font-family: $font-primary;
    font-size: 1rem;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: var(--text-primary);
  }
}

// Mobile Navigation Panel
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: var(--bg-primary, #D0DBEE);
  padding: 2rem 0;
  z-index: 999;

  &__list {
    display: flex;
    flex-direction: column;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
  }

  &__item {
    margin: 1rem 0;

    &--user {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
    }
  }

  &__link {
    font-family: $font-primary;
    font-size: 1.25rem;
    font-weight: 400;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.5rem 0;
  }
}

// User Menu Styles
.user-menu-container {
  position: relative;
}

.user-icon-button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--accent-primary);
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.user-avatar-placeholder {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: var(--accent-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.25rem;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.user-menu {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 16rem;
  background-color: var(--bg-secondary);
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
  
  &__header {
    padding: 1rem;
    background-color: var(--bg-tertiary, #f5f5f5);
  }
  
  &__name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
  }
  
  &__email {
    font-size: 0.875rem;
    color: var(--text-secondary);
  }
  
  &__divider {
    height: 1px;
    background-color: var(--border-color, #e0e0e0);
  }
  
  &__list {
    list-style: none;
    padding: 0.5rem 0;
  }
  
  &__item {
    &:last-child {
      border-top: 1px solid var(--border-color, #e0e0e0);
      margin-top: 0.25rem;
    }
  }
  
  &__link {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: var(--bg-hover, #f0f0f0);
    }
  }
  
  &__button {
    width: 100%;
    text-align: left;
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    color: var(--text-primary);
    transition: background-color 0.2s;
    
    &:hover {
      background-color: var(--bg-hover, #f0f0f0);
    }
    
    &--sign-out {
      color: var(--color-error, #d32f2f);
    }
  }
}

// Mobile Navigation User Styles
.mobile-nav {
  // ... existing styles ...
  
  &__item {
    // ... existing styles ...
    
    &--user {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
    }
  }
  
  &__user-info {
    display: flex;
    align-items: center;
  }
  
  &__user-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--accent-primary);
    margin-right: 1rem;
  }
  
  &__user-avatar-placeholder {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background-color: var(--accent-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.25rem;
    margin-right: 1rem;
  }
  
  &__user-details {
    flex: 1;
  }
  
  &__user-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
  }
  
  &__user-email {
    font-size: 0.875rem;
    color: var(--text-secondary);
  }
  
  &__button {
    width: 100%;
    text-align: left;
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    
    &--sign-out {
      color: var(--color-error, #d32f2f);
    }
  }
}

// Adjust mobile styles to handle the new user position
@media (max-width: 768px) {
  .header {
    &__user-container {
      position: absolute;
      top: 50%;
      right: 4rem; // Give space for the mobile menu trigger
      transform: translateY(-50%); // Center vertically
      height: auto; // Remove fixed height
    }
    
    .user-avatar,
    .user-avatar-placeholder {
      width: 2rem;
      height: 2rem;
      font-size: 1rem;
    }
    
    // Ensure the user menu dropdown appears in the right position
    .user-menu {
      right: 0;
      top: calc(100% + 0.5rem);
      width: 14rem; // Slightly smaller on mobile
    }
  }
}

// Additional mobile-specific adjustments
@media (max-width: 480px) {
  .header {
    &__user-container {
      right: 3.5rem; // Adjust for smaller screens
    }
    
    .user-avatar,
    .user-avatar-placeholder {
      width: 1.75rem;
      height: 1.75rem;
      font-size: 0.875rem;
    }
  }
}




