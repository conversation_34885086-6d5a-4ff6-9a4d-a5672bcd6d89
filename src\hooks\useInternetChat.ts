import { useState, useEffect, useCallback, useRef } from 'react';
import RealWebSocketService, { MessageType, ChatUser } from '../services/realWebSocket';
import InternetWebRTCService, { VoiceCallState } from '../services/internetWebRTC';

interface ChatState {
  users: ChatUser[];
  isConnected: boolean;
  connectionError: string | null;
  currentUser: ChatUser | null;
}

interface UseInternetChatReturn extends ChatState {
  // Connection methods
  connect: (username: string) => Promise<void>;
  disconnect: () => void;
  
  // Voice call methods
  startCall: () => Promise<void>;
  endCall: () => void;
  joinCall: () => Promise<void>;
  leaveCall: () => void;
  toggleMute: () => void;
  
  // Call state
  callState: VoiceCallState;
}

export const useInternetChat = (): UseInternetChatReturn => {
  const [state, setState] = useState<ChatState>({
    users: [],
    isConnected: false,
    connectionError: null,
    currentUser: null
  });
  
  const [callState, setCallState] = useState<VoiceCallState>({
    isInCall: false,
    isMuted: false,
    isDeafened: false,
    participants: [],
    callStartedBy: null,
    callStartTime: null
  });

  const wsServiceRef = useRef<RealWebSocketService | null>(null);
  const webrtcServiceRef = useRef<InternetWebRTCService | null>(null);

  // Connect to gamers room over internet
  const connect = useCallback(async (username: string): Promise<void> => {
    try {
      const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      // Create WebSocket service for gamers room
      wsServiceRef.current = new RealWebSocketService('gamers', userId, username);
      
      // Set up WebSocket listeners
      setupWebSocketListeners();
      
      // Connect to WebSocket server
      await wsServiceRef.current.connect();
      
      // Create WebRTC service
      webrtcServiceRef.current = new InternetWebRTCService(wsServiceRef.current, userId);
      
      // Set up WebRTC listeners
      setupWebRTCListeners();
      
      setState(prev => ({
        ...prev,
        isConnected: true,
        connectionError: null,
        currentUser: {
          id: userId,
          username: username,
          isAdmin: false,
          isInCall: false,
          isMuted: false,
          joinedAt: new Date()
        }
      }));
      
    } catch (error) {
      console.error('Failed to connect to internet chat:', error);
      setState(prev => ({
        ...prev,
        connectionError: error instanceof Error ? error.message : 'Connection failed'
      }));
      throw error;
    }
  }, []);

  // Disconnect from internet chat
  const disconnect = useCallback(() => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.leaveCall();
      webrtcServiceRef.current = null;
    }
    
    if (wsServiceRef.current) {
      wsServiceRef.current.disconnect();
      wsServiceRef.current = null;
    }
    
    setState(prev => ({
      ...prev,
      isConnected: false,
      currentUser: null,
      users: []
    }));
    
    setCallState({
      isInCall: false,
      isMuted: false,
      isDeafened: false,
      participants: [],
      callStartedBy: null,
      callStartTime: null
    });
  }, []);

  // Set up WebSocket event listeners
  const setupWebSocketListeners = useCallback(() => {
    if (!wsServiceRef.current) return;

    const ws = wsServiceRef.current;

    // User management
    ws.on(MessageType.USER_LIST, (users: ChatUser[]) => {
      console.log('Received user list:', users);
      setState(prev => ({ ...prev, users }));
    });

    ws.on(MessageType.USER_JOIN, (userData: ChatUser) => {
      console.log('User joined:', userData);
      setState(prev => ({
        ...prev,
        users: [...prev.users.filter(u => u.id !== userData.id), userData]
      }));
    });

    ws.on(MessageType.USER_LEAVE, (userData: { userId: string, username: string }) => {
      console.log('User left:', userData);
      setState(prev => ({
        ...prev,
        users: prev.users.filter(u => u.id !== userData.userId)
      }));
    });

    // Error handling
    ws.on(MessageType.ERROR, (error: { message: string }) => {
      console.error('WebSocket error:', error);
      setState(prev => ({
        ...prev,
        connectionError: error.message
      }));
    });
  }, []);

  // Set up WebRTC event listeners
  const setupWebRTCListeners = useCallback(() => {
    if (!webrtcServiceRef.current) return;

    const webrtc = webrtcServiceRef.current;

    webrtc.on('stateChange', (newState: VoiceCallState) => {
      console.log('Call state changed:', newState);
      setCallState(newState);
      
      // Update user call status
      setState(prev => ({
        ...prev,
        users: prev.users.map(u => ({
          ...u,
          isInCall: newState.participants.includes(u.id),
          isMuted: u.id === state.currentUser?.id ? newState.isMuted : u.isMuted
        }))
      }));
    });

    webrtc.on('participantJoined', (userId: string) => {
      console.log('Participant joined call:', userId);
    });

    webrtc.on('participantLeft', (userId: string) => {
      console.log('Participant left call:', userId);
    });

    webrtc.on('error', (error: { type: string, message: string }) => {
      console.error('WebRTC error:', error);
      setState(prev => ({
        ...prev,
        connectionError: `Voice call error: ${error.message}`
      }));
    });
  }, [state.currentUser?.id]);

  // Voice call methods
  const startCall = useCallback(async () => {
    if (webrtcServiceRef.current) {
      await webrtcServiceRef.current.startCall();
    }
  }, []);

  const endCall = useCallback(() => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.leaveCall();
    }
  }, []);

  const joinCall = useCallback(async () => {
    if (webrtcServiceRef.current) {
      await webrtcServiceRef.current.joinCall();
    }
  }, []);

  const leaveCall = useCallback(() => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.leaveCall();
    }
  }, []);

  const toggleMute = useCallback(() => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.toggleMute();
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    // State
    ...state,
    callState,
    
    // Methods
    connect,
    disconnect,
    startCall,
    endCall,
    joinCall,
    leaveCall,
    toggleMute
  };
};

export default useInternetChat;
