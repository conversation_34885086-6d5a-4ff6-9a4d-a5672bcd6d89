import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../hooks/useAuth';
import { useChat } from '../hooks/useChat';
import UserAvatar from '../components/common/UserAvatar';
import './Chat.scss';

// Icons (you can replace with react-icons)
const MicIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
    <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
  </svg>
);

const MicOffIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M19 11h-1.7c0 .74-.16 1.43-.43 2.05l1.23 1.23c.56-.98.9-2.09.9-3.28zm-4.02.17c0-.06.02-.11.02-.17V5c0-1.66-1.34-3-3-3S9 3.34 9 5v.18l5.98 5.99zM4.27 3L3 4.27l6.01 6.01V11c0 1.66 1.33 3 2.99 3 .22 0 .44-.03.65-.08l1.66 1.66c-.71.33-1.5.52-2.31.52-2.76 0-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c.57-.08 1.12-.23 1.64-.46l2.36 2.36L21 19.73 4.27 3z"/>
  </svg>
);

const PhoneIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56-.35-.12-.74-.03-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z"/>
  </svg>
);

const PhoneOffIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M17.34 14.54l-1.43-1.43c.56-.73.97-1.54 1.25-2.41l1.9 1.9c-.5 1.04-1.15 2.01-1.72 2.94zm3.57-6.54H18.5c0-.17-.01-.33-.02-.5-.09-1.05-.27-2.06-.54-3.02-.12-.42-.52-.7-.98-.7h-3.27c-.54 0-.99.45-.99.99 0 .23.04.46.09.68l2.5 2.5h5.62zm-1.41 9.19L4.27 2.27 3 3.54l2.76 2.76C5.29 7.5 5 8.74 5 10.01c0 .54.45.99.99.99h3.45c.54 0 .99-.45.99-.99 0-.23-.04-.46-.09-.68L8.46 8.46c-.27.28-.35.67-.24 1.02.37 1.11.56 2.3.56 3.53 0 .54.45.99.99.99h2.46l8.28 8.28 1.27-1.27z"/>
  </svg>
);

const SendIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
  </svg>
);

const Chat: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user, isAuthenticated } = useAuth();
  const {
    users,
    messages,
    typingUsers,
    isConnected,
    connectionError,
    currentUser,
    callState,
    connect,
    disconnect,
    sendMessage,
    startTyping,
    stopTyping,
    startCall,
    endCall,
    joinCall,
    leaveCall,
    toggleMute,
    toggleDeafen,
    kickUser,
    canKickUsers
  } = useChat();

  const [messageInput, setMessageInput] = useState('');
  const [username, setUsername] = useState('');
  const [isJoining, setIsJoining] = useState(false);
  const [showUsernameModal, setShowUsernameModal] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<HTMLInputElement>(null);

  const roomId = searchParams.get('room') || 'general';

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/signup');
    }
  }, [isAuthenticated, navigate]);

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Join chat room
  const handleJoinChat = async () => {
    if (!username.trim()) return;

    setIsJoining(true);
    try {
      await connect(roomId, username.trim());
      setShowUsernameModal(false);
    } catch (error) {
      console.error('Failed to join chat:', error);
    } finally {
      setIsJoining(false);
    }
  };

  // Send message
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (messageInput.trim()) {
      sendMessage(messageInput);
      setMessageInput('');
      stopTyping();
    }
  };

  // Handle typing
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMessageInput(e.target.value);
    if (e.target.value.trim()) {
      startTyping();
    } else {
      stopTyping();
    }
  };

  // Format call duration
  const formatCallDuration = () => {
    if (!callState.callStartTime) return '';
    const duration = Math.floor((Date.now() - callState.callStartTime.getTime()) / 1000);
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="chat-page">
      {/* Username Modal */}
      <AnimatePresence>
        {showUsernameModal && (
          <motion.div
            className="chat-page__modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="chat-page__modal"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <h2>Join Chat Room</h2>
              <p>Room: <strong>{roomId}</strong></p>
              <form onSubmit={(e) => { e.preventDefault(); handleJoinChat(); }}>
                <input
                  type="text"
                  placeholder="Enter your username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  maxLength={20}
                  autoFocus
                />
                <div className="chat-page__modal-actions">
                  <button
                    type="button"
                    onClick={() => navigate('/')}
                    className="btn btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={!username.trim() || isJoining}
                    className="btn btn-primary"
                  >
                    {isJoining ? 'Joining...' : 'Join Chat'}
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Chat Interface */}
      {isConnected && (
        <div className="chat-page__container">
          {/* Header */}
          <div className="chat-page__header">
            <div className="chat-page__room-info">
              <h1>#{roomId}</h1>
              <span className="chat-page__user-count">
                {users.length} user{users.length !== 1 ? 's' : ''} online
              </span>
            </div>

            {/* Call Controls */}
            <div className="chat-page__call-controls">
              {callState.isInCall && (
                <div className="chat-page__call-status">
                  <span className="chat-page__call-indicator">
                    🔴 Live Call ({formatCallDuration()})
                  </span>
                </div>
              )}

              <div className="chat-page__call-buttons">
                {!callState.isInCall ? (
                  <button
                    onClick={startCall}
                    className="chat-page__call-btn chat-page__call-btn--start"
                    title="Start voice call"
                  >
                    <PhoneIcon />
                  </button>
                ) : (
                  <>
                    <button
                      onClick={toggleMute}
                      className={`chat-page__call-btn ${callState.isMuted ? 'chat-page__call-btn--muted' : ''}`}
                      title={callState.isMuted ? 'Unmute' : 'Mute'}
                    >
                      {callState.isMuted ? <MicOffIcon /> : <MicIcon />}
                    </button>
                    <button
                      onClick={leaveCall}
                      className="chat-page__call-btn chat-page__call-btn--end"
                      title="Leave call"
                    >
                      <PhoneOffIcon />
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>

          <div className="chat-page__content">
            {/* User List */}
            <div className="chat-page__sidebar">
              <h3>Users ({users.length})</h3>
              <div className="chat-page__user-list">
                {users.map(user => (
                  <div key={user.id} className="chat-page__user-item">
                    <UserAvatar
                      user={user}
                      size="small"
                      showOnlineStatus={true}
                    />
                    <div className="chat-page__user-info">
                      <span className="chat-page__username">
                        {user.username}
                        {user.isAdmin && <span className="chat-page__admin-badge">Admin</span>}
                      </span>
                      <div className="chat-page__user-status">
                        {user.isInCall && <span className="chat-page__in-call">🔊</span>}
                        {user.isMuted && <span className="chat-page__muted">🔇</span>}
                      </div>
                    </div>
                    {canKickUsers && user.id !== currentUser?.id && (
                      <button
                        onClick={() => kickUser(user.id)}
                        className="chat-page__kick-btn"
                        title="Kick user"
                      >
                        ✕
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Chat Messages */}
            <div className="chat-page__main">
              <div className="chat-page__messages">
                {messages.map(message => (
                  <div
                    key={message.id}
                    className={`chat-page__message ${
                      message.type === 'system' ? 'chat-page__message--system' : ''
                    } ${
                      message.userId === currentUser?.id ? 'chat-page__message--own' : ''
                    }`}
                  >
                    {message.type !== 'system' && (
                      <div className="chat-page__message-avatar">
                        <UserAvatar
                          user={{ id: message.userId, name: message.username } as any}
                          size="small"
                        />
                      </div>
                    )}
                    <div className="chat-page__message-content">
                      {message.type !== 'system' && (
                        <div className="chat-page__message-header">
                          <span className="chat-page__message-username">
                            {message.username}
                          </span>
                          <span className="chat-page__message-time">
                            {message.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                      )}
                      <div className="chat-page__message-text">
                        {message.content}
                      </div>
                    </div>
                  </div>
                ))}

                {/* Typing Indicator */}
                {typingUsers.length > 0 && (
                  <div className="chat-page__typing">
                    <span>
                      {typingUsers.join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...
                    </span>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <form onSubmit={handleSendMessage} className="chat-page__input-form">
                <input
                  ref={messageInputRef}
                  type="text"
                  value={messageInput}
                  onChange={handleInputChange}
                  placeholder="Type a message..."
                  className="chat-page__message-input"
                  maxLength={500}
                />
                <button
                  type="submit"
                  disabled={!messageInput.trim()}
                  className="chat-page__send-btn"
                >
                  <SendIcon />
                </button>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Connection Error */}
      {connectionError && (
        <div className="chat-page__error">
          <p>Connection Error: {connectionError}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      )}
    </div>
  );
};

export default Chat;
