@use '../styles/variables' as *;
@use '../styles/mixins' as *;

.auth-callback-page {
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary);
  padding: 1rem;
  position: relative;
  overflow: hidden;
  
  // Add a subtle pattern overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../assets/images/hero-bg.png');
    background-size: cover;
    background-position: center;
    opacity: 0.05;
    z-index: 0;
  }
}

.auth-callback-container {
  position: relative;
  width: 100%;
  max-width: 28rem;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  background-color: var(--bg-secondary);
  text-align: center;
  z-index: 1;
}

.auth-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  p {
    margin-top: 1rem;
    color: var(--text-secondary);
  }
}

.auth-error {
  h2 {
    color: var(--text-primary);
    margin-bottom: 1rem;
  }
  
  p {
    color: var(--text-error);
    margin-bottom: 1.5rem;
  }
  
  button {
    padding: 0.75rem 1.5rem;
    background-color: var(--color-primary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: var(--color-primary-dark);
    }
  }
}

.spinner {
  width: 40px;
  height: 40px;
  animation: rotate 2s linear infinite;
  
  &__track {
    fill: none;
    stroke: var(--color-border);
    stroke-width: 3;
    stroke-linecap: round;
  }
  
  &__path {
    fill: none;
    stroke: var(--color-primary);
    stroke-width: 3;
    stroke-linecap: round;
    stroke-dasharray: 62.83;
    stroke-dashoffset: 62.83;
    animation: dash 1.5s ease-in-out infinite;
  }
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 62.83;
  }
  50% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -62.83;
  }
}