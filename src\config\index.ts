/**
 * Application configuration
 * Centralized place for all configuration values
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:3001',
  TIMEOUT: 10000, // 10 seconds
  RETRY_ATTEMPTS: 3,
} as const;

// Authentication Configuration
export const AUTH_CONFIG = {
  TOKEN_COOKIE_NAME: 'wolzyn_token',
  USER_COOKIE_NAME: 'wolzyn_user',
  TOKEN_EXPIRES_DAYS: 7,
  GOOGLE_CLIENT_ID: import.meta.env.VITE_GOOGLE_CLIENT_ID || '',
} as const;

// Application Configuration
export const APP_CONFIG = {
  NAME: 'Wolzyn Apparels',
  VERSION: '1.0.0',
  ENVIRONMENT: import.meta.env.MODE || 'development',
  IS_PRODUCTION: import.meta.env.PROD,
  IS_DEVELOPMENT: import.meta.env.DEV,
} as const;

// Theme Configuration
export const THEME_CONFIG = {
  DEFAULT_THEME: 'ascension',
  AVAILABLE_THEMES: ['ascension', 'ember', 'spice'] as const,
} as const;

// Export all configurations
export const CONFIG = {
  API: API_CONFIG,
  AUTH: AUTH_CONFIG,
  APP: APP_CONFIG,
  THEME: THEME_CONFIG,
} as const;

export default CONFIG;
