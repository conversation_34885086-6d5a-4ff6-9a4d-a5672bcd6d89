{"name": "wolzyn", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "build-deploy": "tsc && vite build && aws s3 sync dist/ s3://www.wolzyn.com --delete && aws cloudfront create-invalidation --distribution-id E19TOKPH85Q56T --paths '/*'", "deploy": "aws s3 sync dist/ s3://www.wolzyn.com --delete && aws cloudfront create-invalidation --distribution-id E19TOKPH85Q56T --paths '/*'", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "framer-motion": "^10.16.4", "js-cookie": "^3.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.50.1", "react-icons": "^5.0.1", "react-router-dom": "^6.22.0", "zod": "^3.22.4"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20.11.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "sass": "^1.69.5", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-imagetools": "^7.1.0"}}