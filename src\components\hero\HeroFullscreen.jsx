import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import './HeroFullscreen.scss';
import FogOverlay from './FogOverlay';

const HeroFullscreen = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  
  useEffect(() => {
    // Set loaded state after a short delay to ensure animations trigger
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Animation variants
  const logoVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 1.2,
        ease: [0.6, 0.05, 0.01, 0.9]
      }
    }
  };
  
  const taglineVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.8, 
        delay: 0.6,
        ease: [0.6, 0.05, 0.01, 0.9]
      }
    }
  };
  
  const subheadingVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.8, 
        delay: 0.9,
        ease: [0.6, 0.05, 0.01, 0.9]
      }
    }
  };
  
  const ctaVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.8, 
        delay: 1.2,
        ease: [0.6, 0.05, 0.01, 0.9]
      }
    }
  };

  return (
    <section className="hero-fullscreen">
      <div className="hero-fullscreen__background"></div>
      <FogOverlay />
      
      <div className="hero-fullscreen__content">
        <motion.div 
          className="hero-fullscreen__logo"
          initial="hidden"
          animate={isLoaded ? "visible" : "hidden"}
          variants={logoVariants}
        >
          <span className="icon-Studio-Project-1"></span>
        </motion.div>
        
        <motion.h1 
          className="hero-fullscreen__tagline"
          initial="hidden"
          animate={isLoaded ? "visible" : "hidden"}
          variants={taglineVariants}
        >
          Stories worn, not told.
        </motion.h1>
        
        <motion.p 
          className="hero-fullscreen__subheading"
          initial="hidden"
          animate={isLoaded ? "visible" : "hidden"}
          variants={subheadingVariants}
        >
          Where each T-shirt transforms its wearer into the narrator of our shared mythology.
          Minimalist designs with profound meaning.
        </motion.p>
        
        <motion.div 
          className="hero-fullscreen__cta"
          initial="hidden"
          animate={isLoaded ? "visible" : "hidden"}
          variants={ctaVariants}
        >
          <Link to="/collections" className="hero-fullscreen__button">
            Explore Collections
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroFullscreen;


