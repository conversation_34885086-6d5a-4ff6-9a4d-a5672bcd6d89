.agora-chat {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: system-ui, sans-serif;

  &__modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  &__modal {
    background-color: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;

    h2 {
      margin-bottom: 1rem;
      text-align: center;
      color: #333;
      font-size: 1.75rem;
    }

    p {
      margin-bottom: 1.5rem;
      text-align: center;
      color: #666;
      font-size: 1rem;
    }
  }

  &__form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: #333;
    }

    input, select {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 1rem;
      transition: border-color 0.2s ease;

      &:focus {
        outline: none;
        border-color: #007bff;
      }
    }
  }

  &__checkbox {
    display: flex !important;
    align-items: center;
    cursor: pointer;

    input[type="checkbox"] {
      width: auto !important;
      margin-right: 0.5rem;
      transform: scale(1.2);
    }
  }

  &__modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-bottom: 1.5rem;
  }

  &__btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;

    &--primary {
      background: #007bff;
      color: white;

      &:hover:not(:disabled) {
        background: #0056b3;
        transform: translateY(-1px);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    &--secondary {
      background: transparent;
      color: #666;
      border: 2px solid #ddd;

      &:hover {
        border-color: #007bff;
        color: #007bff;
        transform: translateY(-1px);
      }
    }
  }

  &__info {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #007bff;

    h4 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.125rem;
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;
      
      li {
        margin-bottom: 0.5rem;
        color: #555;
        font-size: 0.875rem;
      }
    }
  }

  &__container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
  }

  &__user-info {
    h2 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.5rem;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 1rem;
    }
  }

  &__leave-btn {
    padding: 0.75rem 1.5rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #c82333;
      transform: translateY(-2px);
    }
  }

  &__agora-container {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    min-height: 500px;

    // Override Agora UI Kit styles
    :global(.agora-uikit-container) {
      border-radius: 12px;
      overflow: hidden;
    }

    :global(.agora-uikit-local-controls) {
      background: rgba(0, 123, 255, 0.1);
      border-radius: 8px;
      padding: 0.5rem;
    }
  }

  &__controls {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &__channel-info {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;

    span {
      color: #555;
      font-weight: 500;
      
      strong {
        color: #333;
      }
    }
  }

  &__footer {
    text-align: center;
  }

  &__back-btn {
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    color: white;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-1px);
    }
  }

  &__error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 2rem;
    text-align: center;
    color: white;

    h2 {
      font-size: 2rem;
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.125rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    button {
      padding: 1rem 2rem;
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      color: white;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .agora-chat {
    &__container {
      padding: 1rem;
    }

    &__header {
      padding: 1rem;
    }

    &__controls {
      padding: 1rem;
    }

    &__channel-info {
      flex-direction: column;
      gap: 0.5rem;
      text-align: center;
    }

    &__agora-container {
      padding: 0.5rem;
      min-height: 400px;
    }
  }
}
