import React from 'react';
import { Link } from 'react-router-dom';
import './ComingSoon.scss';

const ComingSoon = () => {
  return (
    <div className="coming-soon-page">
      <div className="container">
        <div className="coming-soon">
          <div className="coming-soon__icon">
            <span className="icon-studio icon-Studio-Project-1"></span>
          </div>
          <h1 className="coming-soon__title">Coming Soon</h1>
          <p className="coming-soon__message">
            We're weaving something extraordinary. This section of our mythology is still being written.
          </p>
          <div className="coming-soon__countdown">
            <div className="coming-soon__countdown-item">
              <span className="coming-soon__countdown-value">07</span>
              <span className="coming-soon__countdown-label">Days</span>
            </div>
            <div className="coming-soon__countdown-item">
              <span className="coming-soon__countdown-value">12</span>
              <span className="coming-soon__countdown-label">Hours</span>
            </div>
            <div className="coming-soon__countdown-item">
              <span className="coming-soon__countdown-value">34</span>
              <span className="coming-soon__countdown-label">Minutes</span>
            </div>
          </div>
          <div className="coming-soon__actions">
            <Link to="/" className="btn btn-primary">Return Home</Link>
            <a href="#" className="btn btn-secondary" onClick={(e) => {
              e.preventDefault();
              window.history.back();
            }}>Go Back</a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComingSoon;