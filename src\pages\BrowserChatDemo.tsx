import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const BrowserChatDemo: React.FC = () => {
  const navigate = useNavigate();
  const [showInstructions, setShowInstructions] = useState(true);

  return (
    <div style={{ 
      padding: '2rem', 
      maxWidth: '800px', 
      margin: '0 auto',
      fontFamily: 'system-ui, sans-serif',
      lineHeight: 1.6
    }}>
      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <h1 style={{ color: '#333', marginBottom: '1rem' }}>
          🎮 Browser-Only Voice Chat
        </h1>
        <p style={{ color: '#666', fontSize: '1.125rem' }}>
          No backend required! Uses browser capabilities only.
        </p>
      </div>

      {/* How it works */}
      <div style={{ 
        background: '#e7f3ff', 
        padding: '1.5rem', 
        borderRadius: '8px',
        marginBottom: '2rem'
      }}>
        <h2 style={{ marginTop: 0, color: '#333' }}>🔧 How It Works</h2>
        <ul style={{ paddingLeft: '1.5rem', color: '#555' }}>
          <li><strong>BroadcastChannel API:</strong> For cross-tab communication (simulates WebSocket)</li>
          <li><strong>LocalStorage:</strong> Stores user list and room data</li>
          <li><strong>WebRTC:</strong> Direct peer-to-peer voice communication</li>
          <li><strong>MediaDevices API:</strong> Access microphone for voice chat</li>
        </ul>
      </div>

      {/* Features */}
      <div style={{ 
        background: '#f0f9ff', 
        padding: '1.5rem', 
        borderRadius: '8px',
        marginBottom: '2rem'
      }}>
        <h2 style={{ marginTop: 0, color: '#333' }}>✨ Features</h2>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: '1rem' 
        }}>
          <div>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#007bff' }}>🎤 Voice Chat</h4>
            <p style={{ margin: 0, fontSize: '0.9rem', color: '#666' }}>
              Real-time voice communication using WebRTC
            </p>
          </div>
          <div>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#007bff' }}>👥 Multi-User</h4>
            <p style={{ margin: 0, fontSize: '0.9rem', color: '#666' }}>
              Multiple users can join the same room
            </p>
          </div>
          <div>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#007bff' }}>🔇 Mute Control</h4>
            <p style={{ margin: 0, fontSize: '0.9rem', color: '#666' }}>
              Mute/unmute your microphone
            </p>
          </div>
          <div>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#007bff' }}>🌐 No Backend</h4>
            <p style={{ margin: 0, fontSize: '0.9rem', color: '#666' }}>
              Works entirely in the browser
            </p>
          </div>
        </div>
      </div>

      {/* Instructions */}
      {showInstructions && (
        <div style={{ 
          background: '#fff3cd', 
          padding: '1.5rem', 
          borderRadius: '8px',
          marginBottom: '2rem',
          border: '1px solid #ffeaa7'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <div style={{ flex: 1 }}>
              <h2 style={{ marginTop: 0, color: '#856404' }}>📋 Instructions</h2>
              <ol style={{ paddingLeft: '1.5rem', color: '#856404' }}>
                <li style={{ marginBottom: '0.5rem' }}>
                  <strong>Open Multiple Tabs:</strong> Open this page in 2+ browser tabs to simulate multiple users
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <strong>Enter Different Names:</strong> Use different usernames in each tab
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <strong>Join Voice Chat:</strong> Click "Join Voice Chat" in each tab
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <strong>Allow Microphone:</strong> Grant microphone permission when prompted
                </li>
                <li style={{ marginBottom: '0.5rem' }}>
                  <strong>Talk:</strong> You should hear audio from other tabs!
                </li>
              </ol>
            </div>
            <button
              onClick={() => setShowInstructions(false)}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '1.5rem',
                cursor: 'pointer',
                color: '#856404',
                marginLeft: '1rem'
              }}
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Browser Requirements */}
      <div style={{ 
        background: '#f8f9fa', 
        padding: '1.5rem', 
        borderRadius: '8px',
        marginBottom: '2rem'
      }}>
        <h2 style={{ marginTop: 0, color: '#333' }}>🌐 Browser Requirements</h2>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
          <div>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#28a745' }}>✅ Supported</h4>
            <ul style={{ paddingLeft: '1.5rem', color: '#666', fontSize: '0.9rem' }}>
              <li>Chrome 23+</li>
              <li>Firefox 22+</li>
              <li>Safari 11+</li>
              <li>Edge 79+</li>
            </ul>
          </div>
          <div>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#dc3545' }}>⚠️ Required</h4>
            <ul style={{ paddingLeft: '1.5rem', color: '#666', fontSize: '0.9rem' }}>
              <li>HTTPS (for microphone access)</li>
              <li>Modern browser with WebRTC</li>
              <li>Microphone permission</li>
              <li>JavaScript enabled</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Technical Details */}
      <div style={{ 
        background: '#f8f9fa', 
        padding: '1.5rem', 
        borderRadius: '8px',
        marginBottom: '2rem'
      }}>
        <h2 style={{ marginTop: 0, color: '#333' }}>⚙️ Technical Implementation</h2>
        <div style={{ fontSize: '0.9rem', color: '#666' }}>
          <p><strong>MockWebSocketService:</strong> Uses BroadcastChannel API to simulate WebSocket communication between browser tabs</p>
          <p><strong>BrowserWebRTCService:</strong> Handles peer-to-peer voice connections using WebRTC with free STUN servers</p>
          <p><strong>LocalStorage:</strong> Maintains user list and room state across browser sessions</p>
          <p><strong>No Server Required:</strong> Everything runs in the browser - perfect for demos and development</p>
        </div>
      </div>

      {/* Action Buttons */}
      <div style={{ textAlign: 'center', marginTop: '2rem' }}>
        <button
          onClick={() => navigate('/chat')}
          style={{
            padding: '1rem 2rem',
            background: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '1.125rem',
            fontWeight: '600',
            cursor: 'pointer',
            marginRight: '1rem',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#218838';
            e.currentTarget.style.transform = 'translateY(-2px)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = '#28a745';
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          🎮 Try Voice Chat Now
        </button>
        
        <button
          onClick={() => navigate('/')}
          style={{
            padding: '1rem 2rem',
            background: 'transparent',
            color: '#666',
            border: '2px solid #ddd',
            borderRadius: '8px',
            fontSize: '1.125rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.borderColor = '#007bff';
            e.currentTarget.style.color = '#007bff';
            e.currentTarget.style.transform = 'translateY(-2px)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.borderColor = '#ddd';
            e.currentTarget.style.color = '#666';
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          ← Back to Home
        </button>
      </div>

      {/* Footer Note */}
      <div style={{ 
        textAlign: 'center', 
        marginTop: '3rem', 
        padding: '1rem',
        background: '#e9ecef',
        borderRadius: '8px',
        fontSize: '0.875rem',
        color: '#6c757d'
      }}>
        <p style={{ margin: 0 }}>
          💡 <strong>Tip:</strong> Open this page in multiple browser tabs to test the voice chat functionality!
        </p>
      </div>
    </div>
  );
};

export default BrowserChatDemo;
