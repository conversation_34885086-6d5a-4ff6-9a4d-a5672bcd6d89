import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import './Home2.scss';

const Home2 = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  
  useEffect(() => {
    // Set loaded state after a short delay to ensure animations trigger
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);
  
  const toggleMobileNav = () => setMobileNavOpen(!mobileNavOpen);
  
  // Animation variants
  const logoVariants = {
    hidden: { opacity: 0, scale: 0.95, y: 20 },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: { 
        duration: 1.2,
        ease: [0.6, 0.05, 0.01, 0.9]
      }
    }
  };
  
  const navItemVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: i => ({ 
      opacity: 1, 
      x: 0,
      transition: { 
        duration: 0.8, 
        delay: 0.3 + (i * 0.1),
        ease: [0.6, 0.05, 0.01, 0.9]
      }
    })
  };
  
  const navItems = [
    { name: 'HOME', path: '/' },
    { name: 'COLLECTIONS', path: '/collections' },
    { name: 'STORY', path: '/story' },
    { name: 'ABOUT', path: '/about' },
    { name: 'CART', path: '/cart' }
  ];

  return (
    <div className="home2">
      {/* Vertical Navigation - Desktop */}
      <nav className="home2__nav">
        <ul className="home2__nav-list">
          {navItems.map((item, index) => (
            <motion.li 
              key={item.name} 
              className="home2__nav-item"
              custom={index}
              initial="hidden"
              animate={isLoaded ? "visible" : "hidden"}
              variants={navItemVariants}
            >
              <Link to={item.path} className="home2__nav-link">
                {item.name}
              </Link>
            </motion.li>
          ))}
        </ul>
      </nav>
      
      {/* Centered Logo */}
      <motion.div 
        className="home2__logo"
        initial="hidden"
        animate={isLoaded ? "visible" : "hidden"}
        variants={logoVariants}
      >
        {/* Use icon font for logo, same as in HeroFullscreen component */}
        <span className="icon-Studio-Project-1"></span>
        {/* Fallback text logo */}
        <span className="home2__logo-text">WolZyn</span>
      </motion.div>
      
      {/* Mobile Navigation Trigger */}
      <div className="home2__mobile-trigger" onClick={toggleMobileNav}>
        <span className="home2__mobile-trigger-text">
          {mobileNavOpen ? 'CLOSE' : 'MENU'}
        </span>
      </div>
      
      {/* Mobile Navigation Panel */}
      <AnimatePresence>
        {mobileNavOpen && (
          <motion.div 
            className="home2__mobile-nav"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            transition={{ duration: 0.5, ease: [0.16, 1, 0.3, 1] }}
          >
            <ul className="home2__mobile-nav-list">
              {navItems.map((item, index) => (
                <motion.li 
                  key={item.name} 
                  className="home2__mobile-nav-item"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 + (index * 0.1) }}
                >
                  <Link 
                    to={item.path} 
                    className="home2__mobile-nav-link"
                    onClick={() => setMobileNavOpen(false)}
                  >
                    {item.name}
                  </Link>
                </motion.li>
              ))}
            </ul>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Home2;


