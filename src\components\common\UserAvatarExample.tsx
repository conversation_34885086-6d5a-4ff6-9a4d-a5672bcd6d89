import React from 'react';
import UserAvatar, { GoogleUser, UserData } from './UserAvatar';

/**
 * Example component demonstrating how to use UserAvatar with GoogleUser interface
 */
const UserAvatarExample: React.FC = () => {
  // Example GoogleUser with picture
  const googleUserWithPicture: GoogleUser = {
    googleId: 'google_123456789',
    email: '<EMAIL>',
    name: '<PERSON>',
    picture: 'https://lh3.googleusercontent.com/a/default-user=s96-c' // Example Google profile picture URL
  };

  // Example GoogleUser without picture (will show initials)
  const googleUserWithoutPicture: GoogleUser = {
    googleId: 'google_987654321',
    email: '<EMAIL>',
    name: '<PERSON>'
    // picture is optional, so it's omitted
  };

  // Example UserData from email/phone signup
  const emailUser: UserData = {
    id: 'user_email_123',
    email: '<EMAIL>',
    name: '<PERSON><PERSON> User',
    authMethod: 'email',
    isVerified: true
    // No picture provided
  };

  // Example UserData from Google auth (converted from GoogleUser)
  const googleAuthUser: UserData = {
    id: googleUserWithPicture.googleId,
    email: googleUserWithPicture.email,
    name: googleUserWithPicture.name,
    picture: googleUserWithPicture.picture,
    authMethod: 'google',
    isVerified: true
  };

  return (
    <div style={{ padding: '2rem', display: 'flex', flexDirection: 'column', gap: '2rem' }}>
      <h2>UserAvatar Examples</h2>
      
      <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
        <div style={{ textAlign: 'center' }}>
          <h3>Google User with Picture</h3>
          <UserAvatar 
            user={googleUserWithPicture} 
            size="large"
            onClick={() => console.log('Clicked Google user with picture')}
          />
          <p style={{ fontSize: '0.875rem', marginTop: '0.5rem' }}>
            {googleUserWithPicture.name}
          </p>
        </div>

        <div style={{ textAlign: 'center' }}>
          <h3>Google User without Picture</h3>
          <UserAvatar 
            user={googleUserWithoutPicture} 
            size="large"
            onClick={() => console.log('Clicked Google user without picture')}
          />
          <p style={{ fontSize: '0.875rem', marginTop: '0.5rem' }}>
            {googleUserWithoutPicture.name}
          </p>
        </div>

        <div style={{ textAlign: 'center' }}>
          <h3>Email User</h3>
          <UserAvatar 
            user={emailUser} 
            size="large"
            onClick={() => console.log('Clicked email user')}
          />
          <p style={{ fontSize: '0.875rem', marginTop: '0.5rem' }}>
            {emailUser.name}
          </p>
        </div>

        <div style={{ textAlign: 'center' }}>
          <h3>Google Auth User</h3>
          <UserAvatar 
            user={googleAuthUser} 
            size="large"
            showOnlineStatus={true}
            onClick={() => console.log('Clicked Google auth user')}
          />
          <p style={{ fontSize: '0.875rem', marginTop: '0.5rem' }}>
            {googleAuthUser.name}
          </p>
        </div>
      </div>

      <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
        <h3>Different Sizes:</h3>
        <UserAvatar user={googleUserWithPicture} size="small" />
        <UserAvatar user={googleUserWithPicture} size="medium" />
        <UserAvatar user={googleUserWithPicture} size="large" />
        <UserAvatar user={googleUserWithPicture} size="extra-large" />
      </div>

      <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
        <h3>Fallback Initials:</h3>
        <UserAvatar user={googleUserWithoutPicture} size="small" />
        <UserAvatar user={googleUserWithoutPicture} size="medium" />
        <UserAvatar user={googleUserWithoutPicture} size="large" />
        <UserAvatar user={googleUserWithoutPicture} size="extra-large" />
      </div>
    </div>
  );
};

export default UserAvatarExample;
