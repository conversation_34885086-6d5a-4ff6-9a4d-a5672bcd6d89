import React from 'react';
import { motion, useTransform } from 'framer-motion';

const StorytellingTopSection = ({ 
  product, 
  productImage, 
  isInView, 
  tileVariants, 
  y1, 
  y2, 
  selectedColor, 
  handleColorSelect, 
  handleImageChange 
}) => {
  return (
    <div className="storytelling-product__grid-top">
      {/* Tile 1: Product Image - from top left */}
      <motion.div 
        className="storytelling-product__tile storytelling-product__tile--image"
        variants={tileVariants.topLeft}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        style={{ y: y1 }}
        whileHover={{ scale: 1.02 }}
        transition={{ scale: { duration: 0.3, ease: "easeInOut" } }}
      >
        <div className="storytelling-product__image-container">
          <img src={productImage} alt={product.name} />
          {product.isNew && <span className="storytelling-product__badge">New</span>}
        </div>
      </motion.div>

      {/* Tile 2: The Story - from top right */}
      <motion.div 
        className="storytelling-product__tile storytelling-product__tile--story"
        variants={tileVariants.topRight}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        style={{ y: y2 }}
        whileHover={{ scale: 1.02 }}
        transition={{ scale: { duration: 0.3, ease: "easeInOut" } }}
      >
        <div className="storytelling-product__collection">{product.collection}</div>
        <motion.h2 
          className="storytelling-product__name"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          {product.name}
        </motion.h2>
        <div className="storytelling-product__price">${product.price}</div>
        <div className="storytelling-product__story-text">
          <p>{product.story}</p>
        </div>
      </motion.div>
    </div>
  );
};

export default StorytellingTopSection;

