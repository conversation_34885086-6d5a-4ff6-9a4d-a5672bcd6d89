@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.size-guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md;
  animation: fadeIn 0.3s ease-out;
}

.size-guide {
  background-color: var(--bg-primary);
  border-radius: 12px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  animation: scaleIn 0.3s ease-out;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-lg;
    border-bottom: 1px solid var(--border-color);
  }
  
  &__title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
  }
  
  &__close {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: $spacing-xs;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    
    &:hover {
      color: var(--text-primary);
      background-color: var(--bg-secondary);
    }
  }
  
  &__content {
    padding: $spacing-lg;
  }
  
  &__description {
    margin-bottom: $spacing-lg;
    color: var(--text-secondary);
    line-height: 1.6;
  }
  
  &__measurement-diagram {
    position: relative;
    margin-bottom: $spacing-lg;
    display: flex;
    justify-content: center;
    
    img {
      max-width: 100%;
      height: auto;
      opacity: 0.8;
    }
  }
  
  &__measurement-points {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  
  &__measurement-point {
    position: absolute;
    transform: translate(-50%, -50%);
    
    span {
      display: inline-block;
      background-color: var(--accent-primary);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.75rem;
      font-weight: 600;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      
      &::after {
        content: '';
        position: absolute;
        width: 6px;
        height: 6px;
        background-color: var(--accent-primary);
        border-radius: 50%;
        top: 50%;
        left: 100%;
        margin-left: 4px;
        transform: translateY(-50%);
      }
    }
  }
  
  &__table-container {
    margin-bottom: $spacing-lg;
    overflow-x: auto;
  }
  
  &__table {
    width: 100%;
    border-collapse: collapse;
    
    th, td {
      padding: $spacing-sm;
      text-align: center;
      border-bottom: 1px solid var(--border-color);
    }
    
    th {
      font-weight: 600;
      color: var(--text-primary);
      background-color: var(--bg-secondary);
      position: sticky;
      top: 0;
    }
    
    td {
      color: var(--text-secondary);
    }
    
    tr:last-child td {
      border-bottom: none;
    }
    
    tr:nth-child(even) {
      background-color: var(--bg-secondary);
    }
    
    tr:hover {
      background-color: rgba(var(--accent-primary-rgb), 0.05);
    }
  }
  
  &__tips {
    margin-bottom: $spacing-lg;
    
    h3 {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: $spacing-sm;
      color: var(--text-primary);
    }
    
    ul {
      padding-left: $spacing-lg;
      
      li {
        margin-bottom: $spacing-xs;
        color: var(--text-secondary);
        line-height: 1.6;
      }
    }
  }
  
  &__footer {
    padding: $spacing-lg;
    border-top: 1px solid var(--border-color);
    text-align: center;
    
    p {
      margin-bottom: $spacing-md;
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
    
    .btn {
      min-width: 120px;
    }
  }
  
  @media (max-width: $breakpoint-md) {
    &__measurement-diagram {
      display: none;
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); }
  to { transform: scale(1); }
}