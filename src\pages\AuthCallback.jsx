import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import Cookies from 'js-cookie';

// Cookie settings
const COOKIE_EXPIRES = 7; // days
const USER_COOKIE_NAME = 'wolzyn_user';
const TOKEN_COOKIE_NAME = 'wolzyn_token';

const AuthCallback = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [error, setError] = useState(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get token from URL query parameters
        const searchParams = new URLSearchParams(location.search);
        const token = searchParams.get('token');

        if (!token) {
          throw new Error('No authentication token received');
        }

        // Basic token validation (you can add more validation as needed)
        if (token.length < 10) {
          throw new Error('Invalid token format');
        }

        console.log('Auth callback received with token:', token);

        // Store token in cookie with expiry time
        Cookies.set(TOKEN_COOKIE_NAME, token, { 
          expires: COOKIE_EXPIRES,
          sameSite: 'strict',
          secure: window.location.protocol === 'https:'
        });

        // In a real app, you would decode the JWT to get user info
        // For this example, we'll extract some basic info from the token
        // This is a simplified example - in production, use a proper JWT library
        try {
          // Try to decode the token (assuming it's a JWT)
          const tokenParts = token.split('.');
          if (tokenParts.length === 3) {
            // Decode the payload (second part)
            const payload = JSON.parse(atob(tokenParts[1]));
            
            // Create user data from token payload
            const userData = {
              id: payload.sub || `user_${Date.now()}`,
              email: payload.email || null,
              name: payload.name || null,
              picture: payload.picture || null,
              authMethod: 'google',
              isVerified: true
            };
            
            // Store user data in cookie
            Cookies.set(USER_COOKIE_NAME, JSON.stringify(userData), { 
              expires: COOKIE_EXPIRES,
              sameSite: 'strict',
              secure: window.location.protocol === 'https:'
            });
          } else {
            // If token is not a JWT, create basic user data
            const userData = {
              id: `user_${Date.now()}`,
              authMethod: 'google',
              isVerified: true
            };
            
            // Store user data in cookie
            Cookies.set(USER_COOKIE_NAME, JSON.stringify(userData), { 
              expires: COOKIE_EXPIRES,
              sameSite: 'strict',
              secure: window.location.protocol === 'https:'
            });
          }
        } catch (decodeError) {
          console.error('Error decoding token:', decodeError);
          // If decoding fails, create basic user data
          const userData = {
            id: `user_${Date.now()}`,
            authMethod: 'google',
            isVerified: true
          };
          
          // Store user data in cookie
          Cookies.set(USER_COOKIE_NAME, JSON.stringify(userData), { 
            expires: COOKIE_EXPIRES,
            sameSite: 'strict',
            secure: window.location.protocol === 'https:'
          });
        }

        // Redirect to home page
        navigate('/');
      } catch (error) {
        console.error('Auth callback error:', error);
        setError(error.message || 'Authentication failed');
      }
    };

    handleAuthCallback();
  }, [location, navigate]);

  // Show loading or error state
  return (
    <div className="auth-callback-page">
      <div className="auth-callback-container">
        {error ? (
          <div className="auth-error">
            <h2>Authentication Error</h2>
            <p>{error}</p>
            <button onClick={() => navigate('/signup')}>
              Return to Sign Up
            </button>
          </div>
        ) : (
          <div className="auth-loading">
            <svg className="spinner" viewBox="0 0 24 24">
              <circle className="spinner__track" cx="12" cy="12" r="10" />
              <circle className="spinner__path" cx="12" cy="12" r="10" />
            </svg>
            <p>Completing authentication...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthCallback;
