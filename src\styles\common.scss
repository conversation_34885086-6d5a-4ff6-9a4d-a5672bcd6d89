@use 'variables' as *;
@use 'mixins' as *;

// Base layout classes
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

// Grid system
.grid {
  display: grid;
  gap: $spacing-lg;
  
  &--2col {
    grid-template-columns: repeat(1, 1fr);
    
    @media (min-width: $breakpoint-md) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  &--3col {
    grid-template-columns: repeat(1, 1fr);
    
    @media (min-width: $breakpoint-md) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @media (min-width: $breakpoint-lg) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  &--4col {
    grid-template-columns: repeat(1, 1fr);
    
    @media (min-width: $breakpoint-md) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @media (min-width: $breakpoint-lg) {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

// Hero section base styles
.hero-section {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl 0;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    z-index: -1;
  }
  
  &__content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    text-align: center;
    padding: $spacing-lg;
  }
  
  &__title {
    font-family: $font-serif;
    font-size: 3rem;
    margin-bottom: $spacing-md;
    color: var(--text-primary);
    
    @media (max-width: $breakpoint-md) {
      font-size: 2.5rem;
    }
  }
  
  &__subtitle {
    font-size: 1.25rem;
    margin-bottom: $spacing-lg;
    color: var(--text-secondary);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
  }
}

// Card base styles
.card {
  background-color: var(--bg-primary);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
  
  &__title {
    font-family: $font-serif;
    font-size: 1.25rem;
    margin-bottom: $spacing-xs;
    color: var(--text-primary);
  }
  
  &__text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: $spacing-md;
  }
}

// Section base styles
.section {
  padding: $spacing-xl 0;
  
  &__title {
    font-family: $font-serif;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: $spacing-xl;
    color: var(--text-primary);
    
    @media (max-width: $breakpoint-md) {
      font-size: 2rem;
      margin-bottom: $spacing-lg;
    }
  }
}

// Button styles
.btn {
  display: inline-block;
  padding: $spacing-sm $spacing-lg;
  border-radius: 4px;
  font-family: $font-primary;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  
  &-primary {
    background-color: var(--accent-primary);
    color: white;
    
    &:hover {
      background-color: darken(#3a86ff, 10%);
    }
  }
  
  &-secondary {
    background-color: transparent;
    color: var(--accent-primary);
    border: 2px solid var(--accent-primary);
    
    &:hover {
      background-color: var(--accent-primary);
      color: white;
    }
  }
}

// Form elements
.form {
  &__group {
    margin-bottom: $spacing-md;
  }
  
  &__label {
    display: block;
    margin-bottom: $spacing-xs;
    font-weight: 600;
    color: var(--text-primary);
  }
  
  &__input {
    width: 100%;
    padding: $spacing-sm;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: $font-primary;
    font-size: 1rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color 0.3s ease;
    
    &:focus {
      outline: none;
      border-color: var(--accent-primary);
    }
  }
}

// Badge styles
.badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  
  &--primary {
    background-color: var(--accent-primary);
    color: white;
  }
  
  &--secondary {
    background-color: var(--accent-secondary);
    color: white;
  }
  
  &--success {
    background-color: #2ecc71;
    color: white;
  }
  
  &--danger {
    background-color: #e74c3c;
    color: white;
  }
}


