import React, { useEffect, useRef, useState } from 'react';
import { motion, useScroll, useTransform, useInView } from 'framer-motion';
import { Link } from 'react-router-dom';
import StorytellingTopSection from './StorytellingTopSection';
import StorytellingSelectionSection from './StorytellingSelectionSection';
import StorytellingBottomSection from './StorytellingBottomSection';
import './StorytellingProductPage.scss';

const StorytellingProductPage = ({
  product = {
    id: '',
    name: '',
    image: '',
    price: 0,
    story: '',
    colors: [],
    sizes: ['S', 'M', 'L', 'XL'],
    materials: '',
    care: '',
    outcome: {
      title: '',
      keywords: []
    },
    compliment: {
      text: '',
      symbol: ''
    },
    dropTheme: '',
    collection: ''
  }
}) => {
  const containerRef = useRef(null);
  const isInView = useInView(containerRef, { once: false, amount: 0.2 });
  const [selectedSize, setSelectedSize] = useState(null);
  const [selectedColor, setSelectedColor] = useState(null);
  const [productImage, setProductImage] = useState(product.image);
  
  // For parallax effect on scroll
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });
  
  const y1 = useTransform(scrollYProgress, [0, 1], [0, -20]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, -10]);
  const y3 = useTransform(scrollYProgress, [0, 1], [0, -15]);
  const y4 = useTransform(scrollYProgress, [0, 1], [0, -12]);

  // Animation variants for tiles
  const tileVariants = {
    topLeft: {
      hidden: { opacity: 0, x: -50 },
      visible: { 
        opacity: 1, 
        x: 0,
        transition: { 
          duration: 0.8, 
          ease: [0.6, 0.05, 0.01, 0.9],
          delay: 0.1
        }
      }
    },
    topRight: {
      hidden: { opacity: 0, x: 50 },
      visible: { 
        opacity: 1, 
        x: 0,
        transition: { 
          duration: 0.8, 
          ease: [0.6, 0.05, 0.01, 0.9],
          delay: 0.2
        }
      }
    },
    bottomLeft: {
      hidden: { opacity: 0, x: -50 },
      visible: { 
        opacity: 1, 
        x: 0,
        transition: { 
          duration: 0.8, 
          ease: [0.6, 0.05, 0.01, 0.9],
          delay: 0.3
        }
      }
    },
    bottomRight: {
      hidden: { opacity: 0, x: 50 },
      visible: { 
        opacity: 1, 
        x: 0,
        transition: { 
          duration: 0.8, 
          ease: [0.6, 0.05, 0.01, 0.9],
          delay: 0.4
        }
      }
    }
  };

  // Handle color selection
  const handleColorSelect = (color) => {
    setSelectedColor(color);
  };

  // Handle image change based on color
  const handleImageChange = (imageName) => {
    // In a real app, you would change the image based on the color
    // For now, we'll just keep the original image
    setProductImage(product.image);
  };

  return (
    <div className="storytelling-product">
      <div className="storytelling-product__breadcrumb">
        <Link to="/">Home</Link> / <Link to="/collections">Collections</Link> / <span>{product.name}</span>
      </div>

      <div className="storytelling-product__container" ref={containerRef}>
        {/* Top Section with Product Image and Story */}
        <StorytellingTopSection 
          product={product}
          productImage={productImage}
          isInView={isInView}
          tileVariants={tileVariants}
          y1={y1}
          y2={y2}
          selectedColor={selectedColor}
          handleColorSelect={handleColorSelect}
          handleImageChange={handleImageChange}
        />
        
        {/* Middle Section with Color and Size Selection */}
        <StorytellingSelectionSection 
          product={product}
          selectedSize={selectedSize}
          setSelectedSize={setSelectedSize}
          selectedColor={selectedColor}
          handleColorSelect={handleColorSelect}
          handleImageChange={handleImageChange}
          isInView={isInView}
        />
        
        {/* Bottom Section with Outcome and Compliment */}
        <StorytellingBottomSection 
          product={product}
          isInView={isInView}
          tileVariants={tileVariants}
          y3={y3}
          y4={y4}
        />
      </div>
    </div>
  );
};

export default StorytellingProductPage;

