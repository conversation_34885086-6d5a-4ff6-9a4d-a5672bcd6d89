import React from 'react';

interface ImageSource {
  src: string;
  srcset?: string;
}

interface OptimizedImageProps {
  src: string | ImageSource;
  alt: string;
  className?: string;
  width?: number | string;
  height?: number | string;
  loading?: 'lazy' | 'eager';
  sizes?: string;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({ 
  src, 
  alt, 
  className, 
  width, 
  height, 
  loading = 'lazy', 
  sizes = '(max-width: 768px) 100vw, 50vw' 
}) => {
  // For imported images that have been processed by vite-imagetools
  if (typeof src === 'object' && src.src) {
    return (
      <img
        src={src.src}
        srcSet={src.srcset || ''}
        sizes={sizes}
        alt={alt}
        className={className}
        width={width}
        height={height}
        loading={loading}
        decoding="async"
      />
    );
  }
  
  // For regular image paths
  return (
    <img
      src={src}
      alt={alt}
      className={className}
      width={width}
      height={height}
      loading={loading}
      decoding="async"
    />
  );
};

export default OptimizedImage;