import Cookies from 'js-cookie';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';
const TOKEN_COOKIE_NAME = 'wolzyn_token';
const USER_COOKIE_NAME = 'wolzyn_user';

// Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface UserProfile {
  id: string;
  email?: string;
  phone?: string;
  name?: string;
  picture?: string;
  authMethod: 'email' | 'phone' | 'google';
  isVerified: boolean;
  createdAt?: string;
  updatedAt?: string;
  preferences?: {
    theme?: string;
    notifications?: boolean;
    newsletter?: boolean;
  };
}

export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresIn?: number;
}

// API Error class
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Get auth token from cookies
const getAuthToken = (): string | null => {
  return Cookies.get(TOKEN_COOKIE_NAME) || null;
};

// Set auth token in cookies
const setAuthToken = (token: string, expiresIn?: number): void => {
  const expires = expiresIn ? new Date(Date.now() + expiresIn * 1000) : 7; // Default 7 days
  Cookies.set(TOKEN_COOKIE_NAME, token, {
    expires,
    sameSite: 'strict',
    secure: window.location.protocol === 'https:'
  });
};

// Remove auth token
const removeAuthToken = (): void => {
  Cookies.remove(TOKEN_COOKIE_NAME);
  Cookies.remove(USER_COOKIE_NAME);
};

// Base fetch function with auth headers
const apiFetch = async <T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> => {
  const token = getAuthToken();
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
  };

  if (token) {
    defaultHeaders.Authorization = `Bearer ${token}`;
  }

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    
    // Handle different response types
    let data: any;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    if (!response.ok) {
      // Handle specific error cases
      if (response.status === 401) {
        // Token expired or invalid
        removeAuthToken();
        throw new ApiError('Authentication required', 401, 'UNAUTHORIZED');
      }
      
      if (response.status === 403) {
        throw new ApiError('Access forbidden', 403, 'FORBIDDEN');
      }

      const errorMessage = data?.error || data?.message || `HTTP ${response.status}`;
      throw new ApiError(errorMessage, response.status);
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }

    // Network or other errors
    console.error('API request failed:', error);
    throw new ApiError(
      error instanceof Error ? error.message : 'Network error occurred',
      0,
      'NETWORK_ERROR'
    );
  }
};

// Authentication API functions
export const authApi = {
  // Get user profile from backend
  getProfile: async (): Promise<UserProfile> => {
    const response = await apiFetch<UserProfile>('/auth/profile');
    
    if (!response.success || !response.data) {
      throw new ApiError('Failed to fetch user profile');
    }

    // Cache user data in cookie for quick access
    Cookies.set(USER_COOKIE_NAME, JSON.stringify(response.data), {
      expires: 7,
      sameSite: 'strict',
      secure: window.location.protocol === 'https:'
    });

    return response.data;
  },

  // Update user profile
  updateProfile: async (updates: Partial<UserProfile>): Promise<UserProfile> => {
    const response = await apiFetch<UserProfile>('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(updates),
    });

    if (!response.success || !response.data) {
      throw new ApiError('Failed to update user profile');
    }

    // Update cached user data
    Cookies.set(USER_COOKIE_NAME, JSON.stringify(response.data), {
      expires: 7,
      sameSite: 'strict',
      secure: window.location.protocol === 'https:'
    });

    return response.data;
  },

  // Refresh auth token
  refreshToken: async (): Promise<AuthTokens> => {
    const response = await apiFetch<AuthTokens>('/auth/refresh', {
      method: 'POST',
    });

    if (!response.success || !response.data) {
      throw new ApiError('Failed to refresh token');
    }

    // Update stored token
    setAuthToken(response.data.accessToken, response.data.expiresIn);

    return response.data;
  },

  // Logout
  logout: async (): Promise<void> => {
    try {
      await apiFetch('/auth/logout', {
        method: 'POST',
      });
    } catch (error) {
      // Even if logout fails on backend, clear local data
      console.warn('Logout request failed:', error);
    } finally {
      removeAuthToken();
    }
  },

  // Verify token validity
  verifyToken: async (): Promise<boolean> => {
    try {
      const response = await apiFetch('/auth/verify');
      return response.success;
    } catch (error) {
      if (error instanceof ApiError && error.status === 401) {
        removeAuthToken();
      }
      return false;
    }
  },
};

// Export utility functions
export { getAuthToken, setAuthToken, removeAuthToken };
export default apiFetch;
