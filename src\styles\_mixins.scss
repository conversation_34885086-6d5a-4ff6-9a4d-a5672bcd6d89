@use './variables' as *;

// Breakpoints
$breakpoint-mobile: 480px;
$breakpoint-tablet: 768px;
$breakpoint-desktop: 1024px;
$breakpoint-large: 1440px;

// Media query mixins
@mixin mobile {
  @media (min-width: #{$breakpoint-mobile}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-tablet}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-desktop}) {
    @content;
  }
}

@mixin large {
  @media (min-width: #{$breakpoint-large}) {
    @content;
  }
}

// Flexbox mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// Typography mixins
@mixin heading-1 {
  font-family: $font-serif;
  font-size: 2.5rem;
  font-weight: 400;
  line-height: 1.2;
  letter-spacing: 0.02em;
  
  @include tablet {
    font-size: 3.5rem;
  }
  
  @include desktop {
    font-size: 4.5rem;
  }
}

@mixin heading-2 {
  font-family: $font-serif;
  font-size: 2rem;
  font-weight: 400;
  line-height: 1.3;
  @include mobile {
    font-size: 1.75rem;
  }
}

@mixin body-text {
  font-family: $font-primary;
  font-size: 1rem;
  line-height: 1.6;
}

// Animation mixins
@mixin fade-in($duration: 0.3s) {
  animation: fadeIn $duration ease-in-out;
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}

@mixin slide-in($direction: 'up', $distance: 20px, $duration: 0.3s) {
  @if $direction == 'up' {
    animation: slideInUp $duration ease-out;
    
    @keyframes slideInUp {
      from {
        transform: translateY($distance);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }
  } @else if $direction == 'down' {
    animation: slideInDown $duration ease-out;
    
    @keyframes slideInDown {
      from {
        transform: translateY(-$distance);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }
  } @else if $direction == 'left' {
    animation: slideInLeft $duration ease-out;
    
    @keyframes slideInLeft {
      from {
        transform: translateX($distance);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  } @else if $direction == 'right' {
    animation: slideInRight $duration ease-out;
    
    @keyframes slideInRight {
      from {
        transform: translateX(-$distance);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  }
}


