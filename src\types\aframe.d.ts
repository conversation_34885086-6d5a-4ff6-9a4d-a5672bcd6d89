// Type declarations for A-Frame VR framework

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'a-scene': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
        embedded?: boolean;
        'vr-mode-ui'?: string;
        background?: string;
        style?: React.CSSProperties;
      };
      'a-assets': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
      'a-videosphere': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
        src?: string;
        rotation?: string;
      };
      'a-camera': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
        'look-controls'?: string;
        'wasd-controls'?: string;
        position?: string;
      };
      'a-cursor': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
        animation__click?: string;
        animation__fusing?: string;
        raycaster?: string;
        geometry?: string;
        material?: string;
      };
      'a-text': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
        value?: string;
        position?: string;
        align?: string;
        color?: string;
        geometry?: string;
        material?: string;
        events?: any;
      };
      'a-light': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
        type?: string;
        color?: string;
      };
    }
  }

  interface Window {
    AFRAME?: any;
  }
}

export {};
