import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useChat } from '../hooks/useChat';
import { testBrowserCapabilities } from '../utils/browserTest';
import './SimpleChat.scss';

const SimpleChat: React.FC = () => {
  const navigate = useNavigate();
  const {
    users,
    isConnected,
    connectionError,
    callState,
    connect,
    disconnect,
    startCall,
    joinCall,
    leaveCall,
    toggleMute
  } = useChat();

  const [username, setUsername] = useState('');
  const [isJoining, setIsJoining] = useState(false);
  const [showUsernameModal, setShowUsernameModal] = useState(true);
  const [browserSupported, setBrowserSupported] = useState(true);

  // Check browser compatibility on mount
  useEffect(() => {
    const result = testBrowserCapabilities();
    setBrowserSupported(result.supported);

    if (!result.supported) {
      console.error('Browser not supported for voice chat:', result.errors);
    }
  }, []);

  // Join gamers room
  const handleJoinChat = async () => {
    if (!username.trim()) return;

    setIsJoining(true);
    try {
      await connect(username.trim());
      setShowUsernameModal(false);
    } catch (error) {
      console.error('Failed to join chat:', error);
    } finally {
      setIsJoining(false);
    }
  };

  // Format call duration
  const formatCallDuration = () => {
    if (!callState.callStartTime) return '';
    const duration = Math.floor((Date.now() - callState.callStartTime.getTime()) / 1000);
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  // Show browser compatibility warning
  if (!browserSupported) {
    return (
      <div className="simple-chat">
        <div className="simple-chat__error">
          <h2>❌ Browser Not Supported</h2>
          <p>Your browser doesn't support the required features for voice chat:</p>
          <ul style={{ textAlign: 'left', marginBottom: '2rem' }}>
            <li>WebRTC (for voice communication)</li>
            <li>MediaDevices API (for microphone access)</li>
            <li>BroadcastChannel API (for cross-tab communication)</li>
            <li>LocalStorage (for user data)</li>
          </ul>
          <p>Please use a modern browser like Chrome, Firefox, Safari, or Edge.</p>
          <button onClick={() => navigate('/')}>Back to Home</button>
        </div>
      </div>
    );
  }

  return (
    <div className="simple-chat">
      {/* Username Modal */}
      <AnimatePresence>
        {showUsernameModal && (
          <motion.div
            className="simple-chat__modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="simple-chat__modal"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <h2>🎮 Join Gamers Room</h2>
              <p>Enter your name to join the voice chat</p>
              <form onSubmit={(e) => { e.preventDefault(); handleJoinChat(); }}>
                <input
                  type="text"
                  placeholder="Enter your username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  maxLength={20}
                  autoFocus
                  style={{
                    width: '100%',
                    padding: '0.75rem 1rem',
                    border: '2px solid #ddd',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    marginBottom: '1.5rem'
                  }}
                />
                <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
                  <button
                    type="button"
                    onClick={() => navigate('/')}
                    style={{
                      padding: '0.75rem 1.5rem',
                      background: 'transparent',
                      border: '1px solid #ddd',
                      borderRadius: '6px',
                      cursor: 'pointer'
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={!username.trim() || isJoining}
                    style={{
                      padding: '0.75rem 1.5rem',
                      background: '#007bff',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      cursor: username.trim() && !isJoining ? 'pointer' : 'not-allowed',
                      opacity: username.trim() && !isJoining ? 1 : 0.6
                    }}
                  >
                    {isJoining ? 'Joining...' : 'Join Room'}
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Interface */}
      {isConnected && (
        <div className="simple-chat__container">
          <div className="simple-chat__header">
            <h1>🎮 Gamers Voice Chat</h1>
            <p>{users.length} user{users.length !== 1 ? 's' : ''} online</p>
            {callState.isInCall && (
              <div className="simple-chat__call-status">
                🔴 Live Call ({formatCallDuration()})
              </div>
            )}
          </div>

          <div className="simple-chat__room">
            <div className="simple-chat__users">
              <h3>Online Users</h3>
              <div className="simple-chat__user-list">
                {users.map(user => (
                  <div key={user.id} className="simple-chat__user">
                    <div className="simple-chat__user-avatar">
                      {user.username.charAt(0).toUpperCase()}
                    </div>
                    <div className="simple-chat__user-info">
                      <span className="simple-chat__username">{user.username}</span>
                      <div className="simple-chat__user-status">
                        {user.isInCall && <span>🔊 In Call</span>}
                        {user.isMuted && <span>🔇 Muted</span>}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="simple-chat__voice-section">
              <h3>Voice Chat</h3>
              <p>Click to join or leave the voice chat with other gamers!</p>

              {!callState.isInCall ? (
                <button
                  onClick={joinCall}
                  className="simple-chat__voice-btn join"
                >
                  🎤 Join Voice Chat
                </button>
              ) : (
                <div className="simple-chat__voice-controls">
                  <button
                    onClick={toggleMute}
                    className={`simple-chat__voice-btn ${callState.isMuted ? 'muted' : ''}`}
                  >
                    {callState.isMuted ? '🔇 Unmute' : '🎤 Mute'}
                  </button>
                  <button
                    onClick={leaveCall}
                    className="simple-chat__voice-btn leave"
                  >
                    📞 Leave Call
                  </button>
                </div>
              )}
            </div>
          </div>

          <div className="simple-chat__footer">
            <button
              onClick={() => navigate('/')}
              className="simple-chat__back-btn"
            >
              ← Back to Home
            </button>
          </div>
        </div>
      )}

      {/* Connection Error */}
      {connectionError && (
        <div className="simple-chat__error">
          <p>Connection Error: {connectionError}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      )}
    </div>
  );
};

export default SimpleChat;
