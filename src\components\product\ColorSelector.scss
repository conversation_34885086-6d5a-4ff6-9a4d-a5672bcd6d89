@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.color-selector {
  margin-bottom: $spacing-lg;
  
  &__title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: $spacing-sm;
    color: var(--text-primary);
  }
  
  &__options {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-sm;
    margin-bottom: $spacing-sm;
  }
  
  &__btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    padding: 0;
    background: transparent;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    
    &:hover {
      transform: translateY(-2px);
      
      &::after {
        content: attr(title);
        position: absolute;
        bottom: -30px;
        left: 50%;
        transform: translateX(-50%);
        background-color: var(--bg-primary);
        color: var(--text-primary);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        white-space: nowrap;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        z-index: 10;
      }
    }
    
    &--selected {
      .color-selector__swatch {
        transform: scale(1.1);
        
        &::before {
          opacity: 1;
          transform: scale(1);
        }
      }
    }
  }
  
  &__swatch {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    
    // Double circle effect with specific positioning
    &::before {
      content: '';
      position: absolute;
      top: -26%;
      left: -26%;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      border: 2px solid var(--accent-primary);
      opacity: 0;
      transition: all 0.3s ease;
      pointer-events: none;
    }
    
    // Special handling for white color
    &[style*="background-color: white"] {
      border: 1px solid var(--border-color);
    }
    
    // Checkered background for transparent colors
    &[style*="background-color: transparent"] {
      background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                        linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #ccc 75%), 
                        linear-gradient(-45deg, transparent 75%, #ccc 75%);
      background-size: 8px 8px;
      background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
    }
  }
  
  &__selected-name {
    margin-top: $spacing-xs;
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
  }
  
  // Responsive adjustments
  @media (max-width: $breakpoint-sm) {
    &__btn {
      width: 32px;
      height: 32px;
    }
    
    &__swatch {
      width: 18px;
      height: 18px;
      
      &::before {
        width: 24px;
        height: 24px;
      }
    }
  }
}


