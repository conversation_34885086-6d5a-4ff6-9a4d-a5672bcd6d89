@use '../styles/variables' as *;
@use '../styles/mixins' as *;

.profile-page {
  min-height: 100vh;
  padding: 2rem 0;
  background-color: var(--bg-primary);

  &__loading,
  &__error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
    text-align: center;
    
    .spinner {
      width: 3rem;
      height: 3rem;
      border: 3px solid var(--border-color);
      border-top: 3px solid var(--accent-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }
    
    h2 {
      color: var(--text-primary);
      margin-bottom: 1rem;
    }
    
    p {
      color: var(--text-secondary);
      margin-bottom: 2rem;
    }
  }

  &__content {
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      gap: 1.5rem;
    }
  }

  &__avatar-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;

    @media (max-width: $breakpoint-sm) {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }
  }

  &__user-info {
    h1 {
      color: var(--text-primary);
      font-size: 2rem;
      margin-bottom: 0.5rem;
      
      @media (max-width: $breakpoint-sm) {
        font-size: 1.5rem;
      }
    }
  }

  &__email {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  &__status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;

    &--verified {
      background-color: rgba(16, 185, 129, 0.1);
      color: #10b981;
    }

    &--unverified {
      background-color: rgba(245, 158, 11, 0.1);
      color: #f59e0b;
    }
  }

  &__actions {
    display: flex;
    gap: 1rem;

    @media (max-width: $breakpoint-md) {
      width: 100%;
      justify-content: center;
    }
  }

  &__error-message {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border: 1px solid rgba(239, 68, 68, 0.2);
  }

  &__details {
    background-color: var(--bg-primary);
    border-radius: 8px;
    padding: 2rem;
  }

  &__form {
    display: grid;
    gap: 1.5rem;
  }

  &__form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    label {
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    input,
    select {
      padding: 0.75rem 1rem;
      border: 2px solid var(--border-color);
      border-radius: 8px;
      background-color: var(--bg-primary);
      color: var(--text-primary);
      font-size: 1rem;
      transition: border-color 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--accent-primary);
      }

      &::placeholder {
        color: var(--text-secondary);
      }
    }

    &--checkbox {
      flex-direction: row;
      align-items: center;
      gap: 0.75rem;

      label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
        text-transform: none;
        letter-spacing: normal;
        cursor: pointer;
      }

      input[type="checkbox"] {
        width: auto;
        margin: 0;
      }
    }
  }

  &__form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;

    @media (max-width: $breakpoint-sm) {
      flex-direction: column;
    }
  }

  &__info {
    display: grid;
    gap: 1.5rem;
  }

  &__info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);

    &:last-child {
      border-bottom: none;
    }

    label {
      font-weight: 600;
      color: var(--text-secondary);
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    span {
      color: var(--text-primary);
      font-weight: 500;
    }

    @media (max-width: $breakpoint-sm) {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }

  &__auth-method {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--accent-primary);
    color: white;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

// Button styles
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &-primary {
    background-color: var(--accent-primary);
    color: white;

    &:hover:not(:disabled) {
      background-color: var(--accent-secondary);
      transform: translateY(-1px);
    }
  }

  &-secondary {
    background-color: transparent;
    color: var(--accent-primary);
    border: 2px solid var(--accent-primary);

    &:hover:not(:disabled) {
      background-color: var(--accent-primary);
      color: white;
      transform: translateY(-1px);
    }
  }
}

// Spinner animation
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
