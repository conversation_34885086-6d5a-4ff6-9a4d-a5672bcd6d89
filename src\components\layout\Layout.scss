@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  
  &__main {
    flex: 1;
    padding-top: 80px; // Space for fixed navigation
    
    @include tablet {
      padding-top: 90px;
    }
    
    @include desktop {
      padding-top: 100px;
    }
  }
  
  // Add smooth transitions for theme changes
  transition: background-color 0.3s ease, color 0.3s ease;
  
  // Ensure content has proper spacing
  .container {
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 $spacing-md;
    
    @include tablet {
      padding: 0 $spacing-lg;
    }
    
    @include desktop {
      padding: 0 $spacing-xl;
    }
  }
  
  // Add spacing between sections
  section {
    margin-bottom: $spacing-xl;
    
    @include tablet {
      margin-bottom: $spacing-xxl;
    }
  }
  
  // Ensure images are responsive
  img {
    max-width: 100%;
    height: auto;
  }
}