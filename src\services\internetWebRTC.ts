import RealWebSocketService, { MessageType } from './realWebSocket';

// Enhanced WebRTC configuration with TURN servers for internet connectivity
const RTC_CONFIG: RTCConfiguration = {
  iceServers: [
    // Free STUN servers
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
    { urls: 'stun:stun2.l.google.com:19302' },
    { urls: 'stun:stun.services.mozilla.com' },
    
    // Free TURN servers (limited usage)
    {
      urls: 'turn:openrelay.metered.ca:80',
      username: 'openrelayproject',
      credential: 'openrelayproject'
    },
    {
      urls: 'turn:openrelay.metered.ca:443',
      username: 'openrelayproject',
      credential: 'openrelayproject'
    },
    {
      urls: 'turn:openrelay.metered.ca:443?transport=tcp',
      username: 'openrelayproject',
      credential: 'openrelayproject'
    }
  ],
  iceCandidatePoolSize: 10
};

export interface VoiceCallState {
  isInCall: boolean;
  isMuted: boolean;
  isDeafened: boolean;
  participants: string[];
  callStartedBy: string | null;
  callStartTime: Date | null;
}

export class InternetWebRTCService {
  private peerConnections: Map<string, RTCPeerConnection> = new Map();
  private localStream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private callState: VoiceCallState = {
    isInCall: false,
    isMuted: false,
    isDeafened: false,
    participants: [],
    callStartedBy: null,
    callStartTime: null
  };
  private listeners: Map<string, Set<Function>> = new Map();
  private remoteAudioElements: Map<string, HTMLAudioElement> = new Map();
  private connectionAttempts: Map<string, number> = new Map();
  private maxConnectionAttempts = 3;

  constructor(private wsService: RealWebSocketService, private userId: string) {
    this.setupWebSocketListeners();
    this.initializeListeners();
  }

  private initializeListeners() {
    const events = ['stateChange', 'participantJoined', 'participantLeft', 'error'];
    events.forEach(event => {
      this.listeners.set(event, new Set());
    });
  }

  private setupWebSocketListeners() {
    this.wsService.on(MessageType.CALL_START, this.handleCallStart.bind(this));
    this.wsService.on(MessageType.CALL_END, this.handleCallEnd.bind(this));
    this.wsService.on(MessageType.CALL_JOIN, this.handleCallJoin.bind(this));
    this.wsService.on(MessageType.CALL_LEAVE, this.handleCallLeave.bind(this));
    this.wsService.on(MessageType.OFFER, this.handleOffer.bind(this));
    this.wsService.on(MessageType.ANSWER, this.handleAnswer.bind(this));
    this.wsService.on(MessageType.ICE_CANDIDATE, this.handleIceCandidate.bind(this));
  }

  // Initialize audio stream with enhanced settings
  async initializeAudio(): Promise<void> {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000,
          channelCount: 1,
          volume: 1.0
        },
        video: false
      });

      // Set up audio context for advanced processing
      this.audioContext = new AudioContext();
      
      console.log('Audio initialized successfully for internet connectivity');
    } catch (error) {
      console.error('Error accessing microphone:', error);
      this.emit('error', { 
        type: 'microphone', 
        message: 'Could not access microphone. Please allow microphone access and ensure HTTPS.' 
      });
      throw error;
    }
  }

  // Start a voice call
  async startCall(): Promise<void> {
    try {
      if (!this.localStream) {
        await this.initializeAudio();
      }

      this.callState.isInCall = true;
      this.callState.callStartedBy = this.userId;
      this.callState.callStartTime = new Date();
      this.callState.participants = [this.userId];

      this.wsService.startCall();
      this.wsService.updateUserCallStatus(true, this.callState.isMuted);
      this.emit('stateChange', this.callState);
      
      console.log('Call started for internet connectivity');
    } catch (error) {
      console.error('Error starting call:', error);
      this.emit('error', { type: 'call_start', message: 'Failed to start call' });
    }
  }

  // Join an existing call
  async joinCall(): Promise<void> {
    try {
      if (!this.localStream) {
        await this.initializeAudio();
      }

      this.callState.isInCall = true;
      this.callState.participants = [this.userId];
      
      this.wsService.joinCall();
      this.wsService.updateUserCallStatus(true, this.callState.isMuted);
      this.emit('stateChange', this.callState);
      
      console.log('Joined call for internet connectivity');
    } catch (error) {
      console.error('Error joining call:', error);
      this.emit('error', { type: 'call_join', message: 'Failed to join call' });
    }
  }

  // Leave the call
  leaveCall(): void {
    this.peerConnections.forEach((pc, userId) => {
      pc.close();
      this.peerConnections.delete(userId);
    });

    this.remoteAudioElements.forEach((audio, userId) => {
      audio.remove();
    });
    this.remoteAudioElements.clear();

    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    this.callState.isInCall = false;
    this.callState.participants = this.callState.participants.filter(id => id !== this.userId);

    this.wsService.leaveCall();
    this.wsService.updateUserCallStatus(false, false);
    this.emit('stateChange', this.callState);
    
    console.log('Left call');
  }

  // Mute/unmute microphone
  toggleMute(): void {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        this.callState.isMuted = !audioTrack.enabled;
        this.wsService.updateUserCallStatus(this.callState.isInCall, this.callState.isMuted);
        this.emit('stateChange', this.callState);
        console.log(`Microphone ${this.callState.isMuted ? 'muted' : 'unmuted'}`);
      }
    }
  }

  // Create peer connection with enhanced configuration for internet
  private async createPeerConnection(userId: string): Promise<RTCPeerConnection> {
    console.log(`Creating internet peer connection for ${userId}`);
    
    const existingPc = this.peerConnections.get(userId);
    if (existingPc) {
      existingPc.close();
    }
    
    const pc = new RTCPeerConnection(RTC_CONFIG);

    // Add local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => {
        console.log(`Adding track to internet connection for ${userId}:`, track.kind);
        pc.addTrack(track, this.localStream!);
      });
    }

    // Handle incoming stream
    pc.ontrack = (event) => {
      console.log(`Received track from ${userId} over internet:`, event.track.kind);
      const [remoteStream] = event.streams;
      this.handleRemoteStream(userId, remoteStream);
    };

    // Handle ICE candidates with retry logic
    pc.onicecandidate = (event) => {
      if (event.candidate) {
        console.log(`Sending ICE candidate to ${userId} over internet`);
        this.wsService.sendIceCandidate(userId, event.candidate);
      }
    };

    // Enhanced connection state monitoring
    pc.onconnectionstatechange = () => {
      console.log(`Internet connection state with ${userId}:`, pc.connectionState);
      
      if (pc.connectionState === 'connected') {
        console.log(`Successfully connected to ${userId} over internet`);
        this.connectionAttempts.delete(userId);
      } else if (pc.connectionState === 'failed') {
        this.handleConnectionFailure(userId);
      } else if (pc.connectionState === 'disconnected') {
        console.log(`Disconnected from ${userId}`);
        this.handlePeerDisconnection(userId);
      }
    };

    pc.oniceconnectionstatechange = () => {
      console.log(`ICE connection state with ${userId}:`, pc.iceConnectionState);
      
      if (pc.iceConnectionState === 'failed') {
        this.handleConnectionFailure(userId);
      }
    };

    this.peerConnections.set(userId, pc);
    return pc;
  }

  private handleConnectionFailure(userId: string) {
    const attempts = this.connectionAttempts.get(userId) || 0;
    
    if (attempts < this.maxConnectionAttempts) {
      console.log(`Connection failed with ${userId}, retrying (${attempts + 1}/${this.maxConnectionAttempts})`);
      this.connectionAttempts.set(userId, attempts + 1);
      
      // Retry connection after a delay
      setTimeout(() => {
        this.retryConnection(userId);
      }, 2000 * (attempts + 1));
    } else {
      console.error(`Failed to connect to ${userId} after ${this.maxConnectionAttempts} attempts`);
      this.handlePeerDisconnection(userId);
    }
  }

  private async retryConnection(userId: string) {
    try {
      const pc = await this.createPeerConnection(userId);
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      this.wsService.sendOffer(userId, offer);
    } catch (error) {
      console.error(`Error retrying connection to ${userId}:`, error);
    }
  }

  // Handle remote audio stream with enhanced settings
  private handleRemoteStream(userId: string, stream: MediaStream): void {
    console.log(`Setting up remote stream for ${userId} over internet`);
    
    const existingAudio = this.remoteAudioElements.get(userId);
    if (existingAudio) {
      existingAudio.remove();
    }

    const audio = new Audio();
    audio.srcObject = stream;
    audio.autoplay = true;
    audio.volume = this.callState.isDeafened ? 0 : 1;
    
    // Enhanced audio settings for internet connectivity
    audio.crossOrigin = 'anonymous';
    audio.preload = 'auto';
    
    audio.onloadedmetadata = () => {
      console.log(`Audio metadata loaded for ${userId} over internet`);
    };
    
    audio.onplay = () => {
      console.log(`Audio started playing for ${userId} over internet`);
    };
    
    audio.onerror = (error) => {
      console.error(`Audio error for ${userId}:`, error);
    };
    
    audio.style.display = 'none';
    document.body.appendChild(audio);
    
    this.remoteAudioElements.set(userId, audio);
    console.log(`Successfully set up internet audio for ${userId}`);
  }

  // WebSocket event handlers
  private async handleCallStart(payload: any): Promise<void> {
    this.callState.callStartedBy = payload.userId;
    this.callState.callStartTime = new Date();
    this.emit('stateChange', this.callState);
  }

  private handleCallEnd(payload: any): void {
    if (payload.userId !== this.userId) {
      this.leaveCall();
    }
  }

  private async handleCallJoin(payload: any): Promise<void> {
    const { userId } = payload;
    console.log('Handling call join over internet:', userId);
    
    if (userId !== this.userId) {
      if (!this.callState.participants.includes(userId)) {
        this.callState.participants.push(userId);
      }
      
      this.emit('participantJoined', userId);
      this.emit('stateChange', this.callState);

      if (this.callState.isInCall) {
        console.log('Creating internet peer connection for:', userId);
        const pc = await this.createPeerConnection(userId);
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);
        this.wsService.sendOffer(userId, offer);
      }
    }
  }

  private handleCallLeave(payload: any): void {
    const { userId } = payload;
    this.handlePeerDisconnection(userId);
  }

  private async handleOffer(payload: any): Promise<void> {
    const { fromUserId, offer, targetUserId } = payload;
    
    console.log('Received offer over internet from:', fromUserId);
    
    if (targetUserId && targetUserId !== this.userId) return;
    
    try {
      const pc = await this.createPeerConnection(fromUserId);
      await pc.setRemoteDescription(offer);
      const answer = await pc.createAnswer();
      await pc.setLocalDescription(answer);
      this.wsService.sendAnswer(fromUserId, answer);
      console.log('Sent answer over internet to:', fromUserId);
    } catch (error) {
      console.error('Error handling offer over internet:', error);
    }
  }

  private async handleAnswer(payload: any): Promise<void> {
    const { fromUserId, answer, targetUserId } = payload;
    
    console.log('Received answer over internet from:', fromUserId);
    
    if (targetUserId && targetUserId !== this.userId) return;
    
    const pc = this.peerConnections.get(fromUserId);
    if (pc) {
      try {
        await pc.setRemoteDescription(answer);
        console.log('Set remote description for answer over internet from:', fromUserId);
      } catch (error) {
        console.error('Error setting remote description over internet:', error);
      }
    }
  }

  private async handleIceCandidate(payload: any): Promise<void> {
    const { fromUserId, candidate, targetUserId } = payload;
    
    if (targetUserId && targetUserId !== this.userId) return;
    
    const pc = this.peerConnections.get(fromUserId);
    if (pc && candidate) {
      try {
        await pc.addIceCandidate(candidate);
        console.log('Added ICE candidate over internet from:', fromUserId);
      } catch (error) {
        console.error('Error adding ICE candidate over internet:', error);
      }
    }
  }

  private handlePeerDisconnection(userId: string): void {
    const pc = this.peerConnections.get(userId);
    if (pc) {
      pc.close();
      this.peerConnections.delete(userId);
    }

    const audio = this.remoteAudioElements.get(userId);
    if (audio) {
      audio.remove();
      this.remoteAudioElements.delete(userId);
    }

    this.callState.participants = this.callState.participants.filter(id => id !== userId);
    this.connectionAttempts.delete(userId);
    this.emit('participantLeft', userId);
    this.emit('stateChange', this.callState);
  }

  // Event system
  on(event: string, callback: Function): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.add(callback);
    }
  }

  off(event: string, callback: Function): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in WebRTC listener:', error);
        }
      });
    }
  }

  // Getters
  get state(): VoiceCallState {
    return { ...this.callState };
  }

  get isInCall(): boolean {
    return this.callState.isInCall;
  }

  get isMuted(): boolean {
    return this.callState.isMuted;
  }
}

export default InternetWebRTCService;
