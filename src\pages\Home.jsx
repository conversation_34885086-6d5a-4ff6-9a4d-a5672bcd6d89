import React from 'react';
import { Link } from 'react-router-dom';
import FeaturedSection from '../components/sections/FeaturedSection';
import HeroFullscreen from '../components/hero/HeroFullscreen';
import './Home.scss';

// Import images
import rebellionImg from '../assets/images/placeholder-rebellion.jpg';
import ascensionImg from '../assets/images/placeholder-ascension.jpg';
import oracleImg from '../assets/images/placeholder-oracle.jpg';

const Home = () => {
  // Featured collections data
  const featuredCollections = [
    {
      id: 1,
      title: "Rebellion",
      description: "Garments that embody the spirit of defiance and the courage to challenge established norms.",
      image: rebellionImg,
      link: "/collections"
    },
    {
      id: 2,
      title: "Ascension",
      description: "Pieces that represent the human journey toward self-actualization and transcendence.",
      image: ascensionImg,
      link: "/collections"
    },
    {
      id: 3,
      title: "Oracle",
      description: "Designs that channel ancient wisdom and the ability to perceive what others cannot.",
      image: oracleImg,
      link: "/collections"
    }
  ];

  return (
    <div className="home-page">
      {/* Full-screen Hero Section */}
      <HeroFullscreen />

      {/* Featured Collections Section */}
      <FeaturedSection 
        title="Featured Collections" 
        subtitle="Explore our curated collections, each telling a unique story through minimalist design."
        items={featuredCollections}
      />
    </div>
  );
};

export default Home;














