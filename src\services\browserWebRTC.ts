import MockWebSocketService, { MessageType } from './mockWebSocket';

// WebRTC configuration using free STUN servers
const RTC_CONFIG: RTCConfiguration = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
    { urls: 'stun:stun2.l.google.com:19302' },
    { urls: 'stun:stun.services.mozilla.com' }
  ]
};

export interface VoiceCallState {
  isInCall: boolean;
  isMuted: boolean;
  isDeafened: boolean;
  participants: string[];
  callStartedBy: string | null;
  callStartTime: Date | null;
}

export class BrowserWebRTCService {
  private peerConnections: Map<string, RTCPeerConnection> = new Map();
  private localStream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private gainNode: GainNode | null = null;
  private callState: VoiceCallState = {
    isInCall: false,
    isMuted: false,
    isDeafened: false,
    participants: [],
    callStartedBy: null,
    callStartTime: null
  };
  private listeners: Map<string, Set<Function>> = new Map();
  private remoteAudioElements: Map<string, HTMLAudioElement> = new Map();

  constructor(private wsService: MockWebSocketService, private userId: string) {
    this.setupWebSocketListeners();
    this.initializeListeners();
  }

  private initializeListeners() {
    const events = ['stateChange', 'participantJoined', 'participantLeft', 'error'];
    events.forEach(event => {
      this.listeners.set(event, new Set());
    });
  }

  private setupWebSocketListeners() {
    this.wsService.on(MessageType.CALL_START, this.handleCallStart.bind(this));
    this.wsService.on(MessageType.CALL_END, this.handleCallEnd.bind(this));
    this.wsService.on(MessageType.CALL_JOIN, this.handleCallJoin.bind(this));
    this.wsService.on(MessageType.CALL_LEAVE, this.handleCallLeave.bind(this));
    this.wsService.on(MessageType.OFFER, this.handleOffer.bind(this));
    this.wsService.on(MessageType.ANSWER, this.handleAnswer.bind(this));
    this.wsService.on(MessageType.ICE_CANDIDATE, this.handleIceCandidate.bind(this));
  }

  // Initialize audio stream
  async initializeAudio(): Promise<void> {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000
        },
        video: false
      });

      // Set up audio context for volume control
      this.audioContext = new AudioContext();

      console.log('Audio initialized successfully');
    } catch (error) {
      console.error('Error accessing microphone:', error);
      this.emit('error', { type: 'microphone', message: 'Could not access microphone. Please allow microphone access.' });
      throw error;
    }
  }

  // Start a voice call
  async startCall(): Promise<void> {
    try {
      if (!this.localStream) {
        await this.initializeAudio();
      }

      this.callState.isInCall = true;
      this.callState.callStartedBy = this.userId;
      this.callState.callStartTime = new Date();
      this.callState.participants = [this.userId];

      this.wsService.startCall();
      this.wsService.updateUserCallStatus(true, this.callState.isMuted);
      this.emit('stateChange', this.callState);

      console.log('Call started');
    } catch (error) {
      console.error('Error starting call:', error);
      this.emit('error', { type: 'call_start', message: 'Failed to start call' });
    }
  }

  // End the voice call
  endCall(): void {
    this.peerConnections.forEach((pc, userId) => {
      pc.close();
      this.peerConnections.delete(userId);
    });

    // Remove all remote audio elements
    this.remoteAudioElements.forEach((audio, userId) => {
      audio.remove();
    });
    this.remoteAudioElements.clear();

    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.callState = {
      isInCall: false,
      isMuted: false,
      isDeafened: false,
      participants: [],
      callStartedBy: null,
      callStartTime: null
    };

    this.wsService.endCall();
    this.wsService.updateUserCallStatus(false, false);
    this.emit('stateChange', this.callState);

    console.log('Call ended');
  }

  // Join an existing call
  async joinCall(): Promise<void> {
    try {
      if (!this.localStream) {
        await this.initializeAudio();
      }

      this.callState.isInCall = true;
      this.callState.participants = [this.userId]; // Start with ourselves

      this.wsService.joinCall();
      this.wsService.updateUserCallStatus(true, this.callState.isMuted);
      this.emit('stateChange', this.callState);

      console.log('Joined call, participants:', this.callState.participants);
    } catch (error) {
      console.error('Error joining call:', error);
      this.emit('error', { type: 'call_join', message: 'Failed to join call' });
    }
  }

  // Leave the call
  leaveCall(): void {
    this.peerConnections.forEach((pc, userId) => {
      pc.close();
      this.peerConnections.delete(userId);
    });

    // Remove all remote audio elements
    this.remoteAudioElements.forEach((audio, userId) => {
      audio.remove();
    });
    this.remoteAudioElements.clear();

    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    this.callState.isInCall = false;
    this.callState.participants = this.callState.participants.filter(id => id !== this.userId);

    this.wsService.leaveCall();
    this.wsService.updateUserCallStatus(false, false);
    this.emit('stateChange', this.callState);

    console.log('Left call');
  }

  // Mute/unmute microphone
  toggleMute(): void {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        this.callState.isMuted = !audioTrack.enabled;
        this.wsService.updateUserCallStatus(this.callState.isInCall, this.callState.isMuted);
        this.emit('stateChange', this.callState);
        console.log(`Microphone ${this.callState.isMuted ? 'muted' : 'unmuted'}`);
      }
    }
  }

  // Create peer connection for a user
  private async createPeerConnection(userId: string): Promise<RTCPeerConnection> {
    console.log(`Creating peer connection for ${userId}`);

    // Close existing connection if any
    const existingPc = this.peerConnections.get(userId);
    if (existingPc) {
      existingPc.close();
      console.log(`Closed existing connection for ${userId}`);
    }

    const pc = new RTCPeerConnection(RTC_CONFIG);

    // Add local stream to peer connection
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => {
        console.log(`Adding track to peer connection for ${userId}:`, track.kind);
        pc.addTrack(track, this.localStream!);
      });
    } else {
      console.warn(`No local stream available when creating connection for ${userId}`);
    }

    // Handle incoming stream
    pc.ontrack = (event) => {
      console.log(`Received track from ${userId}:`, event.track.kind);
      const [remoteStream] = event.streams;
      this.handleRemoteStream(userId, remoteStream);
    };

    // Handle ICE candidates
    pc.onicecandidate = (event) => {
      if (event.candidate) {
        console.log(`Sending ICE candidate to ${userId}`);
        this.wsService.sendIceCandidate(userId, event.candidate);
      } else {
        console.log(`ICE gathering complete for ${userId}`);
      }
    };

    // Handle connection state changes
    pc.onconnectionstatechange = () => {
      console.log(`Connection state with ${userId}:`, pc.connectionState);
      if (pc.connectionState === 'connected') {
        console.log(`Successfully connected to ${userId}`);
      } else if (pc.connectionState === 'failed' || pc.connectionState === 'disconnected') {
        console.log(`Connection failed/disconnected with ${userId}`);
        this.handlePeerDisconnection(userId);
      }
    };

    // Handle ICE connection state changes
    pc.oniceconnectionstatechange = () => {
      console.log(`ICE connection state with ${userId}:`, pc.iceConnectionState);
    };

    this.peerConnections.set(userId, pc);
    console.log(`Peer connection created for ${userId}`);
    return pc;
  }

  // Handle remote audio stream
  private handleRemoteStream(userId: string, stream: MediaStream): void {
    console.log(`Setting up remote stream for user ${userId}`, stream);

    // Remove existing audio element if any
    const existingAudio = this.remoteAudioElements.get(userId);
    if (existingAudio) {
      existingAudio.remove();
      console.log(`Removed existing audio element for ${userId}`);
    }

    const audio = new Audio();
    audio.srcObject = stream;
    audio.autoplay = true;
    audio.volume = this.callState.isDeafened ? 0 : 1;

    // Add event listeners for debugging
    audio.onloadedmetadata = () => {
      console.log(`Audio metadata loaded for ${userId}`);
    };

    audio.onplay = () => {
      console.log(`Audio started playing for ${userId}`);
    };

    audio.onerror = (error) => {
      console.error(`Audio error for ${userId}:`, error);
    };

    // Hide the audio element
    audio.style.display = 'none';
    document.body.appendChild(audio);

    this.remoteAudioElements.set(userId, audio);

    console.log(`Successfully set up audio for user ${userId}`);
  }

  // WebSocket event handlers
  private async handleCallStart(payload: any): Promise<void> {
    this.callState.callStartedBy = payload.userId;
    this.callState.callStartTime = new Date();
    this.emit('stateChange', this.callState);
  }

  private handleCallEnd(payload: any): void {
    if (payload.userId !== this.userId) {
      // Someone else ended the call
      this.endCall();
    }
  }

  private async handleCallJoin(payload: any): Promise<void> {
    const { userId } = payload;
    console.log('Handling call join:', userId, 'current user:', this.userId);

    if (userId !== this.userId) {
      // Add to participants if not already there
      if (!this.callState.participants.includes(userId)) {
        this.callState.participants.push(userId);
      }

      this.emit('participantJoined', userId);
      this.emit('stateChange', this.callState);

      // If we're in call, create peer connection
      if (this.callState.isInCall) {
        console.log('Creating peer connection for:', userId);
        const pc = await this.createPeerConnection(userId);
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);
        this.wsService.sendOffer(userId, offer);
      }
    }
  }

  private handleCallLeave(payload: any): void {
    const { userId } = payload;
    this.handlePeerDisconnection(userId);
  }

  private async handleOffer(payload: any): Promise<void> {
    const { fromUserId, offer, targetUserId } = payload;

    console.log('Received offer from:', fromUserId, 'to:', targetUserId, 'we are:', this.userId);

    // Only handle offers directed to us or broadcast offers
    if (targetUserId && targetUserId !== this.userId) return;

    try {
      const pc = await this.createPeerConnection(fromUserId);

      await pc.setRemoteDescription(offer);
      const answer = await pc.createAnswer();
      await pc.setLocalDescription(answer);

      this.wsService.sendAnswer(fromUserId, answer);
      console.log('Sent answer to:', fromUserId);
    } catch (error) {
      console.error('Error handling offer:', error);
    }
  }

  private async handleAnswer(payload: any): Promise<void> {
    const { fromUserId, answer, targetUserId } = payload;

    console.log('Received answer from:', fromUserId, 'to:', targetUserId, 'we are:', this.userId);

    // Only handle answers directed to us
    if (targetUserId && targetUserId !== this.userId) return;

    const pc = this.peerConnections.get(fromUserId);

    if (pc) {
      try {
        await pc.setRemoteDescription(answer);
        console.log('Set remote description for answer from:', fromUserId);
      } catch (error) {
        console.error('Error setting remote description:', error);
      }
    } else {
      console.warn('No peer connection found for:', fromUserId);
    }
  }

  private async handleIceCandidate(payload: any): Promise<void> {
    const { fromUserId, candidate, targetUserId } = payload;

    // Only handle candidates directed to us
    if (targetUserId && targetUserId !== this.userId) return;

    const pc = this.peerConnections.get(fromUserId);

    if (pc && candidate) {
      try {
        await pc.addIceCandidate(candidate);
        console.log('Added ICE candidate from:', fromUserId);
      } catch (error) {
        console.error('Error adding ICE candidate:', error);
      }
    }
  }

  private handlePeerDisconnection(userId: string): void {
    const pc = this.peerConnections.get(userId);
    if (pc) {
      pc.close();
      this.peerConnections.delete(userId);
    }

    // Remove audio element
    const audio = this.remoteAudioElements.get(userId);
    if (audio) {
      audio.remove();
      this.remoteAudioElements.delete(userId);
    }

    this.callState.participants = this.callState.participants.filter(id => id !== userId);
    this.emit('participantLeft', userId);
    this.emit('stateChange', this.callState);
  }

  // Event system
  on(event: string, callback: Function): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.add(callback);
    }
  }

  off(event: string, callback: Function): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in WebRTC listener:', error);
        }
      });
    }
  }

  // Getters
  get state(): VoiceCallState {
    return { ...this.callState };
  }

  get isInCall(): boolean {
    return this.callState.isInCall;
  }

  get isMuted(): boolean {
    return this.callState.isMuted;
  }
}

export default BrowserWebRTCService;
