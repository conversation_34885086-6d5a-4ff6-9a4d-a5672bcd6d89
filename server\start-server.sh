#!/bin/bash

# W<PERSON><PERSON>n Apparels Voice Chat Server Startup Script

echo "🚀 Starting Wolzyn Apparels Voice Chat Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Navigate to server directory
cd "$(dirname "$0")"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing server dependencies..."
    npm install
fi

# Set default port if not specified
if [ -z "$PORT" ]; then
    export PORT=3001
fi

echo "🌐 Server will run on port $PORT"
echo "📡 WebSocket endpoint: ws://localhost:$PORT"
echo "🏥 Health check: http://localhost:$PORT/health"
echo ""
echo "🎮 Voice chat is ready for internet connections!"
echo "💡 Make sure to update VITE_WEBSOCKET_URL in your .env file"
echo ""

# Start the server
echo "▶️  Starting server..."
npm start
