.fog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  opacity: var(--fog-opacity, 0.2);
  
  &__layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    
    &--1 {
      animation: fog-drift-1 var(--fog-animation-duration, 200s) linear infinite;
    }
    
    &--2 {
      animation: fog-drift-2 var(--fog-animation-duration, 200s) linear infinite;
      animation-delay: calc(var(--fog-animation-duration, 200s) * -0.5);
    }
  }
  
  &__image {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background-size: cover;
    background-position: center;
    will-change: transform;
  }
}

@keyframes fog-drift-1 {
  0% {
    transform: translateX(-25%) translateY(0%);
  }
  100% {
    transform: translateX(25%) translateY(0%);
  }
}

@keyframes fog-drift-2 {
  0% {
    transform: translateX(25%) translateY(0%);
  }
  100% {
    transform: translateX(-25%) translateY(0%);
  }
}