@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;
@use '../../styles/common';

.featured-section {
  padding: $spacing-xxl 0;
  position: relative;
  overflow: hidden;
  
  // Futuristic background elements
  &::before, &::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    opacity: 0.05;
    z-index: -1;
  }
  
  &::before {
    width: 400px;
    height: 400px;
    top: -200px;
    left: -200px;
  }
  
  &::after {
    width: 600px;
    height: 600px;
    bottom: -300px;
    right: -300px;
  }
  
  &__header {
    text-align: center;
    margin-bottom: $spacing-xl;
    position: relative;
    
    // Decorative line
    &::after {
      content: '';
      position: absolute;
      bottom: -$spacing-md;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    }
  }
  
  &__title {
    font-family: $font-serif;
    font-size: 2.75rem;
    margin-bottom: $spacing-md;
    color: var(--text-primary);
    position: relative;
    display: inline-block;
    
    @media (max-width: $breakpoint-md) {
      font-size: 2.25rem;
    }
    
    // Futuristic underline
    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 8px;
      background: linear-gradient(90deg, var(--accent-primary), transparent);
      opacity: 0.2;
      border-radius: 4px;
    }
  }
  
  &__title-accent {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
  
  &__subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
  
  &__grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: $spacing-xl;
    margin-bottom: $spacing-xl;
    
    @media (min-width: $breakpoint-md) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @media (min-width: $breakpoint-lg) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  &__footer {
    text-align: center;
    margin-top: $spacing-xl;
  }
  
  &__view-all {
    display: inline-flex;
    align-items: center;
    font-weight: 600;
    font-size: 1.125rem;
    color: var(--accent-primary);
    text-decoration: none;
    padding: $spacing-sm $spacing-lg;
    border-radius: 30px;
    background: rgba(var(--accent-primary-rgb), 0.05);
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(var(--accent-primary-rgb), 0.1);
      transform: translateY(-3px);
      
      .featured-section__arrow {
        transform: translateX(5px);
      }
    }
  }
  
  &__arrow {
    margin-left: $spacing-xs;
    transition: transform 0.3s ease;
  }
}
