@use './styles/reset';
@use './styles/variables' as *;
@use './styles/mixins' as *;
@use './styles/common';  // Import common styles first
@use './styles/typography';
@use './styles/animations';
@use './pages/Signup.scss';  // Import Signup styles
@use './pages/AuthCallback.scss';  // Import AuthCallback styles
@use "sass:color";
@use "sass:map";
@import './assets/font.css';

body {
  font-family: $font-primary;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

// Keep the Ascension theme
[data-theme='ascension'] {
  --bg-primary: #{map.get($theme-ascension, primary)};
  --bg-primary-rgb: 245, 247, 250; // RGB value of the primary background color
  --bg-secondary: #{map.get($theme-ascension, secondary)};
  --bg-tertiary: #{map.get($theme-ascension, tertiary)};
  --text-primary: #{map.get($theme-ascension, text)};
  --text-secondary: #{color.scale(map.get($theme-ascension, text), $alpha: -30%)};
  --accent-primary: #{map.get($theme-ascension, accent)};
  --accent-secondary: #{color.scale(map.get($theme-ascension, accent), $lightness: -10%)};
  --logo-icon-color: #{map.get($theme-ascension, accent)};
  --logo-text-color: #{map.get($theme-ascension, text)};
  --border-color: #{map.get($theme-ascension, tertiary)};
  --shadow-color: #{color.scale(map.get($theme-ascension, text), $alpha: -90%)};
}

// Add the Forged Ember theme
[data-theme='ember'] {
  --bg-primary: #{map.get($theme-ember, primary)};
  --bg-primary-rgb: 228, 227, 224; // RGB value of the primary background color
  --bg-secondary: #{map.get($theme-ember, secondary)};
  --bg-tertiary: #{map.get($theme-ember, tertiary)};
  --text-primary: #{map.get($theme-ember, text)};
  --text-secondary: #{color.scale(map.get($theme-ember, text), $alpha: -30%)};
  --accent-primary: #{map.get($theme-ember, accent)};
  --accent-secondary: #{color.scale(map.get($theme-ember, accent), $lightness: -10%)};
  --logo-icon-color: #{map.get($theme-ember, accent)};
  --logo-text-color: #{map.get($theme-ember, text)};
  --border-color: #{map.get($theme-ember, tertiary)};
  --shadow-color: #{color.scale(map.get($theme-ember, text), $alpha: -90%)};
}

// Add the Cayenne Spice theme
[data-theme='spice'] {
  --bg-primary: #{map.get($theme-spice, primary)};
  --bg-primary-rgb: 228, 227, 224; // RGB value of the primary background color
  --bg-secondary: #{map.get($theme-spice, secondary)};
  --bg-tertiary: #{map.get($theme-spice, tertiary)};
  --text-primary: #{map.get($theme-spice, text)};
  --text-secondary: #{color.scale(map.get($theme-spice, text), $alpha: -30%)};
  --accent-primary: #{map.get($theme-spice, accent)};
  --accent-secondary: #{color.scale(map.get($theme-spice, accent), $lightness: -10%)};
  --logo-icon-color: #{map.get($theme-spice, accent)};
  --logo-text-color: #{map.get($theme-spice, text)};
  --border-color: #{map.get($theme-spice, tertiary)};
  --shadow-color: #{color.scale(map.get($theme-spice, text), $alpha: -90%)};
}
















