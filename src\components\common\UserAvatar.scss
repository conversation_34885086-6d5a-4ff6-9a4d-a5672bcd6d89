@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.user-avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  transition: all 0.2s ease;

  &--clickable {
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
    }

    &:focus {
      outline: 2px solid var(--accent-primary);
      outline-offset: 2px;
    }
  }

  // Size variants
  &--small {
    width: 1.75rem;
    height: 1.75rem;

    .user-avatar__placeholder {
      font-size: 0.75rem;
    }
  }

  &--medium {
    width: 2.5rem;
    height: 2.5rem;

    .user-avatar__placeholder {
      font-size: 1rem;
    }
  }

  &--large {
    width: 3.5rem;
    height: 3.5rem;

    .user-avatar__placeholder {
      font-size: 1.25rem;
    }
  }

  &--extra-large {
    width: 5rem;
    height: 5rem;

    .user-avatar__placeholder {
      font-size: 1.75rem;
    }
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    border: 2px solid var(--accent-primary);
  }

  &__placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary, #6366f1));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    border: 2px solid var(--accent-primary);
    border-radius: 50%;
    user-select: none;
  }

  &__status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    border: 2px solid var(--background-primary, white);

    &--online {
      background-color: #10b981; // Green for online
    }

    &--away {
      background-color: #f59e0b; // Yellow for away
    }

    &--offline {
      background-color: #6b7280; // Gray for offline
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    &--large {
      width: 3rem;
      height: 3rem;

      .user-avatar__placeholder {
        font-size: 1.125rem;
      }
    }

    &--extra-large {
      width: 4rem;
      height: 4rem;

      .user-avatar__placeholder {
        font-size: 1.5rem;
      }
    }
  }

  @media (max-width: 480px) {
    &--medium {
      width: 2rem;
      height: 2rem;

      .user-avatar__placeholder {
        font-size: 0.875rem;
      }
    }

    &--large {
      width: 2.5rem;
      height: 2.5rem;

      .user-avatar__placeholder {
        font-size: 1rem;
      }
    }

    &--extra-large {
      width: 3.5rem;
      height: 3.5rem;

      .user-avatar__placeholder {
        font-size: 1.25rem;
      }
    }
  }
}

// Animation for avatar loading
@keyframes avatarPulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

.user-avatar--loading {
  .user-avatar__placeholder {
    animation: avatarPulse 1.5s ease-in-out infinite;
  }
}