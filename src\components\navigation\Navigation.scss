@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.navigation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 1.5rem 0;
  transition: all 0.3s ease;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    backdrop-filter: blur(0px);
    transition: all 0.3s ease;
    z-index: -1;
  }
  
  &--scrolled {
    padding: 1rem 0;
    
    &::before {
      background-color: rgba(var(--bg-primary-rgb), 0.85);
      backdrop-filter: blur(10px);
    }
  }
  
  &__container {
    @include flex-between;
  }
  
  &__logo {
    display: block;
    z-index: 1001;
    text-decoration: none;
    
    &-text {
      font-family: $font-logo;
      font-size: 1.75rem;
      color: var(--text-primary);
      letter-spacing: 0.02em;
    }
  }
  
  &__menu-toggle {
    display: block;
    z-index: 1001;
    cursor: pointer;
    width: 30px;
    height: 30px;
    position: relative;
    
    @include desktop {
      display: none;
    }
  }
  
  &__menu-icon {
    display: block;
    position: relative;
    width: 24px;
    height: 2px;
    background-color: var(--text-primary);
    transition: all 0.3s ease;
    
    &::before,
    &::after {
      content: '';
      position: absolute;
      width: 24px;
      height: 2px;
      background-color: var(--text-primary);
      transition: all 0.3s ease;
    }
    
    &::before {
      top: -8px;
    }
    
    &::after {
      bottom: -8px;
    }
    
    &--open {
      background-color: transparent;
      
      &::before {
        top: 0;
        transform: rotate(45deg);
      }
      
      &::after {
        bottom: 0;
        transform: rotate(-45deg);
      }
    }
  }
  
  &__menu {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    background-color: var(--bg-primary);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    
    @include desktop {
      position: static;
      height: auto;
      background-color: transparent;
      flex-direction: row;
      width: auto;
      transform: none !important;
    }
  }
  
  &__list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    @include desktop {
      flex-direction: row;
      margin-right: 2rem;
    }
  }
  
  &__item {
    margin: 1rem 0;
    
    @include desktop {
      margin: 0 1.5rem;
    }
  }
  
  &__link {
    font-family: $font-primary;
    font-size: 1.5rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
    
    @include desktop {
      font-size: 1rem;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -4px;
        left: 0;
        width: 0;
        height: 2px;
        background-color: var(--accent-primary);
        transition: width 0.3s ease;
      }
      
      &:hover::after,
      &--active::after {
        width: 100%;
      }
    }
    
    &:hover {
      color: var(--accent-primary);
    }
    
    &--active {
      color: var(--accent-primary);
    }
  }
  
  &__theme-toggle {
    margin-top: 2rem;
    padding: 0.75rem 1.5rem;
    background-color: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: $font-primary;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    
    @include desktop {
      margin-top: 0;
    }
    
    &:hover {
      background-color: rgba(var(--accent-primary-rgb), 0.1);
      border-color: var(--accent-primary);
    }
  }
  
  &__theme-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border-radius: 50%;
    background-color: var(--accent-primary);
  }
}
