import React, { useState } from 'react';
import { motion } from 'framer-motion';
import SizeSelector from './SizeSelector';
import ColorSelector from './ColorSelector';
import SizeGuide from './SizeGuide';

const ProductSelectionSection = ({ 
  product, 
  selectedSize, 
  setSelectedSize, 
  selectedColor, 
  setSelectedColor, 
  handleColorSelect, 
  handleImageChange, 
  slideUpVariants 
}) => {
  const [isSizeGuideOpen, setIsSizeGuideOpen] = useState(false);
  
  const handleSizeSelect = (size) => {
    setSelectedSize(size);
  };
  
  const openSizeGuide = () => {
    setIsSizeGuideOpen(true);
  };
  
  const closeSizeGuide = () => {
    setIsSizeGuideOpen(false);
  };
  
  return (
    <motion.div 
      className="product-page__selection-section"
      variants={slideUpVariants}
      initial="hidden"
      animate="visible"
      layout
    >
      <div className="product-page__selection-container">
        {/* Color Selector Component */}
        {product.colors && product.colors.length > 0 && (
          <div className="product-page__colors">
            <div className="product-page__section-title">
              <h3>Color: <span>{selectedColor}</span></h3>
            </div>
            <ColorSelector
              colors={product.colors}
              selectedColor={selectedColor}
              onColorSelect={handleColorSelect}
              onImageChange={handleImageChange}
            />
          </div>
        )}

        {/* Size Selector Component */}
        <div className="product-page__sizes">
          <div className="product-page__section-title">
            <h3>Size: <span>{selectedSize || "Select a size"}</span></h3>
            <button className="product-page__size-guide-btn" onClick={openSizeGuide}>
              Size Guide
            </button>
          </div>
          <SizeSelector
            sizes={product.sizes}
            selectedSize={selectedSize}
            onSizeSelect={handleSizeSelect}
          />
        </div>

        <button
          className={`product-page__add-to-cart btn btn-primary ${!selectedSize ? 'btn-disabled' : ''}`}
          disabled={!selectedSize}
        >
          {selectedSize ? 'Add to Cart' : 'Select a Size'}
        </button>
      </div>
      
      {/* Size Guide Modal */}
      <SizeGuide isOpen={isSizeGuideOpen} onClose={closeSizeGuide} />
    </motion.div>
  );
};

export default ProductSelectionSection;