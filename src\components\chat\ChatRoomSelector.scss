@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.chat-room-selector {
  min-height: 100vh;
  padding: 2rem 0;
  background-color: var(--bg-primary);

  &__content {
    max-width: 1200px;
    margin: 0 auto;
  }

  &__header {
    text-align: center;
    margin-bottom: 3rem;

    h1 {
      color: var(--text-primary);
      font-size: 2.5rem;
      margin-bottom: 1rem;
      font-weight: 700;

      @media (max-width: $breakpoint-md) {
        font-size: 2rem;
      }
    }

    p {
      color: var(--text-secondary);
      font-size: 1.125rem;
      max-width: 600px;
      margin: 0 auto;
    }
  }

  &__rooms {
    margin-bottom: 3rem;

    h2 {
      color: var(--text-primary);
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
      font-weight: 600;
    }
  }

  &__room-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;

    @media (max-width: $breakpoint-sm) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  &__room-card {
    background-color: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: var(--accent-primary);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  &__room-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    h3 {
      color: var(--text-primary);
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0;
    }
  }

  &__room-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
  }

  &__status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    
    &.active {
      background-color: #10b981;
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
    }
    
    &.inactive {
      background-color: #6b7280;
    }
  }

  &__user-count {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
  }

  &__room-description {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 1.5rem;
  }

  &__join-btn {
    width: 100%;
    padding: 0.75rem 1.5rem;
    background-color: var(--accent-primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--accent-secondary);
      transform: translateY(-1px);
    }
  }

  // Custom room section
  &__custom {
    margin-bottom: 3rem;
    background-color: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--border-color);

    h2 {
      color: var(--text-primary);
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
      font-weight: 600;
    }
  }

  &__custom-form {
    max-width: 500px;
  }

  &__input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;

    @media (max-width: $breakpoint-sm) {
      flex-direction: column;
      gap: 0.75rem;
    }
  }

  &__custom-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;

    &:focus {
      outline: none;
      border-color: var(--accent-primary);
    }

    &::placeholder {
      color: var(--text-secondary);
    }
  }

  &__custom-btn {
    padding: 0.75rem 2rem;
    background-color: var(--accent-primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;

    &:hover:not(:disabled) {
      background-color: var(--accent-secondary);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    @media (max-width: $breakpoint-sm) {
      width: 100%;
    }
  }

  &__custom-note {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-style: italic;
  }

  // Features section
  &__features {
    margin-bottom: 3rem;

    h2 {
      color: var(--text-primary);
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
      font-weight: 600;
      text-align: center;
    }
  }

  &__feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;

    @media (max-width: $breakpoint-sm) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  &__feature {
    text-align: center;
    padding: 1.5rem;
    background-color: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);

    h4 {
      color: var(--text-primary);
      margin: 1rem 0 0.5rem 0;
      font-weight: 600;
    }

    p {
      color: var(--text-secondary);
      font-size: 0.875rem;
      line-height: 1.5;
    }
  }

  &__feature-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  // Quick actions
  &__quick-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  &__quick-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 150px;

    &--primary {
      background-color: var(--accent-primary);
      color: white;

      &:hover {
        background-color: var(--accent-secondary);
        transform: translateY(-2px);
      }
    }

    &--secondary {
      background-color: transparent;
      color: var(--text-primary);
      border: 2px solid var(--border-color);

      &:hover {
        border-color: var(--accent-primary);
        color: var(--accent-primary);
        transform: translateY(-2px);
      }
    }

    @media (max-width: $breakpoint-sm) {
      width: 100%;
      min-width: auto;
    }
  }
}
