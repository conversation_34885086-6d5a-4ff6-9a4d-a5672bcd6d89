import React, { useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { AuthService, makeAuthenticatedRequest } from '../../services/authService';
import UserAvatar from '../common/UserAvatar';

/**
 * Example component demonstrating how to use auth/profile endpoints
 * This shows different ways to interact with the authentication system
 */
const AuthExample: React.FC = () => {
  const { user, isLoading, isAuthenticated, error, updateProfile, refreshProfile } = useAuth();
  const [customApiResult, setCustomApiResult] = useState<string>('');
  const [isUpdating, setIsUpdating] = useState(false);

  // Example 1: Using the useAuth hook (recommended)
  const handleQuickUpdate = async () => {
    setIsUpdating(true);
    try {
      await updateProfile({
        preferences: {
          ...user?.preferences,
          theme: user?.preferences?.theme === 'ascension' ? 'ember' : 'ascension'
        }
      });
      console.log('Theme toggled successfully');
    } catch (error) {
      console.error('Failed to toggle theme:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  // Example 2: Using AuthService directly
  const handleServiceUpdate = async () => {
    const result = await AuthService.updateUserProfile({
      name: `${user?.name || 'User'} (Updated via Service)`
    });

    if (result.success) {
      console.log('Profile updated via AuthService:', result.data);
      // Refresh the profile to see changes
      await refreshProfile();
    } else {
      console.error('Service update failed:', result.error);
    }
  };

  // Example 3: Making custom authenticated API calls
  const handleCustomApiCall = async () => {
    // Example of calling a custom endpoint that requires authentication
    const result = await makeAuthenticatedRequest('/auth/profile', {
      method: 'GET'
    });

    if (result.success) {
      setCustomApiResult(`Custom API call successful: ${JSON.stringify(result.data, null, 2)}`);
    } else {
      setCustomApiResult(`Custom API call failed: ${result.error}`);
    }
  };

  // Example 4: Check authentication status
  const handleCheckAuth = async () => {
    const isAuth = await AuthService.isAuthenticated();
    console.log('Is authenticated:', isAuth);
    alert(`Authentication status: ${isAuth ? 'Authenticated' : 'Not authenticated'}`);
  };

  if (isLoading) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <div>Loading authentication state...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <h2>Authentication Required</h2>
        <p>Please log in to see the auth examples.</p>
        <a href="/signup" style={{ color: 'var(--accent-primary)' }}>
          Go to Sign Up
        </a>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center', color: 'red' }}>
        <h2>Authentication Error</h2>
        <p>{error}</p>
        <button onClick={refreshProfile}>Retry</button>
      </div>
    );
  }

  return (
    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Authentication & Profile API Examples</h1>
      
      {/* User Info Display */}
      <div style={{ 
        background: 'var(--bg-secondary)', 
        padding: '1.5rem', 
        borderRadius: '8px', 
        marginBottom: '2rem',
        display: 'flex',
        alignItems: 'center',
        gap: '1rem'
      }}>
        <UserAvatar user={user!} size="large" />
        <div>
          <h3>{user?.name || 'Unknown User'}</h3>
          <p>Email: {user?.email || 'No email'}</p>
          <p>Auth Method: {user?.authMethod}</p>
          <p>Theme: {user?.preferences?.theme || 'default'}</p>
          <p>Verified: {user?.isVerified ? 'Yes' : 'No'}</p>
        </div>
      </div>

      {/* Example Buttons */}
      <div style={{ display: 'grid', gap: '1rem', marginBottom: '2rem' }}>
        <h2>API Usage Examples</h2>
        
        <div>
          <h3>1. Using useAuth Hook</h3>
          <button 
            onClick={handleQuickUpdate}
            disabled={isUpdating}
            style={{ 
              padding: '0.75rem 1.5rem', 
              background: 'var(--accent-primary)', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {isUpdating ? 'Updating...' : 'Toggle Theme (useAuth)'}
          </button>
          <p style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>
            Uses the useAuth hook to update profile preferences
          </p>
        </div>

        <div>
          <h3>2. Using AuthService</h3>
          <button 
            onClick={handleServiceUpdate}
            style={{ 
              padding: '0.75rem 1.5rem', 
              background: 'var(--accent-secondary)', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Update Name (AuthService)
          </button>
          <p style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>
            Uses AuthService.updateUserProfile() directly
          </p>
        </div>

        <div>
          <h3>3. Custom Authenticated Request</h3>
          <button 
            onClick={handleCustomApiCall}
            style={{ 
              padding: '0.75rem 1.5rem', 
              background: '#10b981', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Make Custom API Call
          </button>
          <p style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>
            Uses makeAuthenticatedRequest() for custom endpoints
          </p>
        </div>

        <div>
          <h3>4. Check Authentication</h3>
          <button 
            onClick={handleCheckAuth}
            style={{ 
              padding: '0.75rem 1.5rem', 
              background: '#f59e0b', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Check Auth Status
          </button>
          <p style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>
            Uses AuthService.isAuthenticated() to verify token
          </p>
        </div>

        <div>
          <h3>5. Refresh Profile</h3>
          <button 
            onClick={refreshProfile}
            style={{ 
              padding: '0.75rem 1.5rem', 
              background: '#6366f1', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Refresh Profile Data
          </button>
          <p style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>
            Fetches fresh profile data from backend
          </p>
        </div>
      </div>

      {/* API Result Display */}
      {customApiResult && (
        <div style={{ 
          background: '#f3f4f6', 
          padding: '1rem', 
          borderRadius: '4px',
          marginTop: '2rem'
        }}>
          <h3>Custom API Result:</h3>
          <pre style={{ 
            fontSize: '0.875rem', 
            overflow: 'auto',
            whiteSpace: 'pre-wrap'
          }}>
            {customApiResult}
          </pre>
        </div>
      )}

      {/* Code Examples */}
      <div style={{ marginTop: '3rem' }}>
        <h2>Code Examples</h2>
        
        <div style={{ marginBottom: '2rem' }}>
          <h3>Backend Endpoint Structure</h3>
          <pre style={{ 
            background: '#1f2937', 
            color: '#f9fafb', 
            padding: '1rem', 
            borderRadius: '4px',
            fontSize: '0.875rem',
            overflow: 'auto'
          }}>
{`// GET /auth/profile
{
  "success": true,
  "data": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "picture": "https://example.com/avatar.jpg",
    "authMethod": "google",
    "isVerified": true,
    "preferences": {
      "theme": "ascension",
      "notifications": true,
      "newsletter": false
    }
  }
}`}
          </pre>
        </div>

        <div>
          <h3>Frontend Usage</h3>
          <pre style={{ 
            background: '#1f2937', 
            color: '#f9fafb', 
            padding: '1rem', 
            borderRadius: '4px',
            fontSize: '0.875rem',
            overflow: 'auto'
          }}>
{`// Using useAuth hook
const { user, updateProfile } = useAuth();

// Update profile
await updateProfile({
  name: 'New Name',
  preferences: { theme: 'ember' }
});

// Using AuthService
const result = await AuthService.getUserProfile();
if (result.success) {
  console.log(result.data);
}`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default AuthExample;
