import React from 'react';
import { Link } from 'react-router-dom';
import ProductCard from '../product/ProductCard';
import './FeaturedSection.scss';

const FeaturedSection = ({ products = [] }) => {
  return (
    <section className="featured-section">
      <div className="container">
        <div className="featured-section__header">
          <h2 className="featured-section__title">
            <span className="featured-section__title-accent">Featured</span> Collections
          </h2>
          <p className="featured-section__subtitle">
            Discover our most iconic designs, each telling a unique story
          </p>
        </div>
        
        <div className="featured-section__grid">
          {products.map(product => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
        
        <div className="featured-section__footer">
          <Link to="/collections" className="featured-section__view-all">
            View All Collections
            <span className="featured-section__arrow">→</span>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedSection;