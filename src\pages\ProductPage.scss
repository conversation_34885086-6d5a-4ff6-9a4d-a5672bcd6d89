@use '../styles/variables' as *;
@use '../styles/mixins' as *;

.product-page {
  padding: $spacing-xl 0;
  
  &__top-section,
  &__bottom-section {
    margin-bottom: $spacing-xl;
  }
  
  &__selection-section {
    margin: $spacing-xl 0;
    background-color: var(--bg-secondary);
    border-radius: 12px;
    padding: $spacing-lg;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  }
  
  &__selection-container {
    max-width: 800px;
    margin: 0 auto;
  }
  
  &__section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;
    
    h3 {
      font-size: 1rem;
      font-weight: 600;
      
      span {
        font-weight: 400;
        color: var(--text-secondary);
      }
    }
  }
  
  &__size-guide-btn {
    background: none;
    border: none;
    color: var(--accent-primary);
    font-size: 0.875rem;
    text-decoration: underline;
    cursor: pointer;
    padding: 0;
    
    &:hover {
      color: var(--accent-secondary);
    }
  }
  
  &__colors,
  &__sizes {
    margin-bottom: $spacing-lg;
  }
  
  &__loading,
  &__not-found {
    min-height: 60vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    
    h2 {
      margin-bottom: $spacing-md;
      font-size: 2rem;
    }
    
    p {
      margin-bottom: $spacing-lg;
      color: var(--text-secondary);
    }
  }
  
  &__breadcrumb {
    margin-bottom: $spacing-lg;
    font-size: 0.875rem;
    color: var(--text-secondary);
    
    a {
      color: var(--text-secondary);
      text-decoration: none;
      transition: color 0.3s ease;
      
      &:hover {
        color: var(--accent-primary);
      }
    }
    
    span {
      color: var(--text-primary);
    }
  }
  
  &__content {
    display: grid;
    grid-template-columns: 1fr;
    gap: $spacing-xl;
    
    @media (min-width: $breakpoint-lg) {
      grid-template-columns: 1fr 1fr;
    }
  }
  
  &__gallery {
    position: relative;
  }
  
  &__main-image {
    position: relative;
    overflow: hidden;
    
    img {
      width: 100%;
      height: auto;
      display: block;
    }
  }
  
  &__badge {
    position: absolute;
    top: $spacing-md;
    right: $spacing-md;
    padding: 6px 12px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-radius: 30px;
    z-index: 2;
    
    &--new {
      background: linear-gradient(135deg, #ff9a8b 0%, #ff6a88 100%);
      color: white;
    }
  }
  
  &__info {
    display: flex;
    flex-direction: column;
  }
  
  &__collection {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--accent-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: $spacing-xs;
  }
  
  &__name {
    font-family: $font-serif;
    font-size: 2.5rem;
    margin-bottom: $spacing-sm;
    color: var(--text-primary);
    
    @media (max-width: $breakpoint-md) {
      font-size: 2rem;
    }
  }
  
  &__price {
    font-weight: 700;
    font-size: 1.5rem;
    margin-bottom: $spacing-lg;
    color: var(--text-primary);
    
    &::after {
      content: '';
      display: block;
      width: 40px;
      height: 2px;
      background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
      margin-top: $spacing-md;
    }
  }
  
  &__description {
    margin-bottom: $spacing-lg;
    
    p {
      font-size: 1rem;
      line-height: 1.8;
      color: var(--text-secondary);
    }
  }
  
  &__sizes {
    margin-bottom: $spacing-lg;
    
    h3 {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: $spacing-sm;
    }
  }
  
  &__size-options {
    display: flex;
    gap: $spacing-sm;
  }
  
  &__size-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-color);
    background: transparent;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--accent-primary);
      color: var(--accent-primary);
    }
    
    &--selected {
      background: var(--accent-primary);
      border-color: var(--accent-primary);
      color: white;
    }
  }
  
  &__add-to-cart {
    margin-bottom: $spacing-xl;
    padding: $spacing-md $spacing-lg;
    width: 100%;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    
    &::before {
      content: '';
      display: inline-block;
      width: 18px;
      height: 18px;
      margin-right: $spacing-sm;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z'/%3E%3C/svg%3E");
      background-size: contain;
      background-repeat: no-repeat;
      vertical-align: middle;
    }
  }
  
  &__details {
    border-top: 1px solid var(--border-color);
    padding-top: $spacing-lg;
  }
  
  &__detail {
    margin-bottom: $spacing-md;
    
    h3 {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: $spacing-xs;
    }
    
    p {
      font-size: 0.95rem;
      color: var(--text-secondary);
    }
  }
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  
  &:hover {
    transform: none;
    box-shadow: none;
  }
}



