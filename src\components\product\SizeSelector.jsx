import React from 'react';
import './SizeSelector.scss';

const SizeSelector = ({ sizes=[], selectedSize, onSizeSelect, hideLabel = false }) => {
  // Standard size order for proper sorting
  const sizeOrder = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'OVERSIZE'];
  
  // Sort sizes according to standard order
  const sortedSizes = [...sizes].sort((a, b) => {
    return sizeOrder.indexOf(a) - sizeOrder.indexOf(b);
  });

  return (
    <div className="size-selector">
      {!hideLabel && <h3 className="size-selector__title">Size</h3>}
      
      <div className="size-selector__options">
        {sortedSizes.map(size => (
          <button 
            key={size} 
            className={`size-selector__btn ${selectedSize === size ? 'size-selector__btn--selected' : ''}`}
            onClick={() => onSizeSelect(size)}
            aria-label={`Select size ${size}`}
          >
            {size}
          </button>
        ))}
      </div>
    </div>
  );
};

export default SizeSelector;

