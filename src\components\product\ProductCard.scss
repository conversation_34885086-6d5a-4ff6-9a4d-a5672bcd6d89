@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;
@use '../../styles/common';

.product-card {
  position: relative;
  background-color: var(--bg-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  height: 100%; // Ensure consistent height
  display: flex;
  flex-direction: column;
  
  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    
    .product-card__image {
      transform: scale(1.08);
    }
    
    .product-card__overlay {
      opacity: 1;
    }
  }
  
  // Ensure animations maintain layout integrity
  &.animate-fade-in,
  &.animate-slide-up,
  &.animate-slide-left,
  &.animate-slide-right {
    will-change: transform, opacity;
    transform-origin: center center;
  }
  
  // Ensure final state matches expected layout
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
  }
  
  // Futuristic accent line
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    z-index: 1;
    opacity: 0.7;
    transition: height 0.4s ease, opacity 0.4s ease;
  }
  
  &__image-container {
    position: relative;
    overflow: hidden;
    aspect-ratio: 3/4;
  }
  
  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
  }
  
  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.7) 0%,
      rgba(0, 0, 0, 0.3) 40%,
      rgba(0, 0, 0, 0) 100%
    );
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding-bottom: $spacing-lg;
    opacity: 0;
    transition: opacity 0.4s ease;
  }
  
  &__btn {
    padding: $spacing-sm $spacing-lg;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
    border-radius: 30px;
    text-decoration: none;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.25);
      transform: translateY(-3px);
    }
  }
  
  &__badge {
    position: absolute;
    top: $spacing-md;
    right: $spacing-md;
    padding: 6px 12px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-radius: 30px;
    z-index: 2;
    
    &--new {
      background: linear-gradient(135deg, #ff9a8b 0%, #ff6a88 100%);
      color: white;
    }
    
    &--featured {
      background: linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%);
      color: white;
    }
  }
  
  &__info {
    padding: $spacing-lg;
    position: relative;
  }
  
  &__collection {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--accent-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: $spacing-xs;
  }
  
  &__name {
    font-family: $font-serif;
    font-size: 1.25rem;
    margin-bottom: $spacing-xs;
    
    &-link {
      color: var(--text-primary);
      text-decoration: none;
      transition: color 0.3s ease;
      
      &:hover {
        color: var(--accent-primary);
      }
    }
  }
  
  &__description {
    font-size: 0.95rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: $spacing-md;
  }
  
  &__price {
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--text-primary);
    
    &::before {
      content: '';
      position: absolute;
      top: $spacing-lg;
      right: $spacing-lg;
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
      opacity: 0.1;
      border-radius: 50%;
      z-index: 0;
    }
  }
}

