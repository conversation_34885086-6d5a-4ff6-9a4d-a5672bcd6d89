import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import './Collections.scss';

// Placeholder images
import product1 from '../assets/images/placeholder-rebellion.jpg';
import product2 from '../assets/images/placeholder-ascension.jpg';
import product3 from '../assets/images/placeholder-oracle.jpg';

const Collections = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  
  // Dummy product data
  const products = [
    {
      id: 1,
      name: "The Prometheus Tee",
      price: 45,
      collection: "rebellion",
      image: product1,
      description: "Inspired by the titan who defied the gods to bring fire to humanity.",
      sizes: ["S", "M", "L", "XL"],
      isNew: true,
      isBestseller: false
    },
    {
      id: 2,
      name: "Icarus Flight",
      price: 45,
      collection: "ascension",
      image: product2,
      description: "A reminder that even in failure, there is glory in daring greatly.",
      sizes: ["S", "M", "L", "XL"],
      isNew: false,
      isBestseller: true
    },
    {
      id: 3,
      name: "Oracle of Delphi",
      price: 50,
      collection: "oracle",
      image: product3,
      description: "Channeling the wisdom of the ancient oracle in minimalist form.",
      sizes: ["S", "M", "L", "XL"],
      isNew: true,
      isBestseller: false
    },
    {
      id: 4,
      name: "Sisyphus Strength",
      price: 45,
      collection: "rebellion",
      image: product1,
      description: "Finding meaning in the eternal struggle, inspired by Camus.",
      sizes: ["S", "M", "L", "XL"],
      isNew: false,
      isBestseller: true
    },
    {
      id: 5,
      name: "Ascension Path",
      price: 45,
      collection: "ascension",
      image: product2,
      description: "The journey from ordinary to extraordinary, woven into fabric.",
      sizes: ["S", "M", "L", "XL"],
      isNew: true,
      isBestseller: false
    },
    {
      id: 6,
      name: "Cassandra's Vision",
      price: 50,
      collection: "oracle",
      image: product3,
      description: "Honoring the seer whose truths were destined to be disbelieved.",
      sizes: ["S", "M", "L", "XL"],
      isNew: false,
      isBestseller: false
    },
    {
      id: 7,
      name: "Ambitious Journey",
      price: 55,
      collection: "ambitious",
      image: product2, // Using placeholder image
      description: "Embrace the path of growth with quiet determination and resilience.",
      sizes: ["S", "M", "L", "XL"],
      isNew: true,
      isBestseller: false
    },
    {
      id: 8,
      name: "Sage Wisdom",
      price: 50,
      collection: "ambitious",
      image: product3, // Using placeholder image
      description: "Channeling the calm clarity that comes from balanced ambition.",
      sizes: ["S", "M", "L", "XL"],
      isNew: false,
      isBestseller: true
    }
  ];
  
  // Filter products based on active filter
  const filteredProducts = activeFilter === 'all' 
    ? products 
    : products.filter(product => product.collection === activeFilter);
  
  return (
    <div className="collections-page">
      {/* Filters Section */}
      <section className="collections-filters">
        <div className="container">
          <div className="collections-filters__wrapper">
            <button 
              className={`collections-filters__btn ${activeFilter === 'all' ? 'active' : ''}`}
              onClick={() => setActiveFilter('all')}
            >
              All Collections
            </button>
            <button 
              className={`collections-filters__btn ${activeFilter === 'rebellion' ? 'active' : ''}`}
              onClick={() => setActiveFilter('rebellion')}
            >
              Rebellion
            </button>
            <button 
              className={`collections-filters__btn ${activeFilter === 'ascension' ? 'active' : ''}`}
              onClick={() => setActiveFilter('ascension')}
            >
              Ascension
            </button>
            <button 
              className={`collections-filters__btn ${activeFilter === 'oracle' ? 'active' : ''}`}
              onClick={() => setActiveFilter('oracle')}
            >
              Oracle
            </button>
            <button 
              className={`collections-filters__btn ${activeFilter === 'ambitious' ? 'active' : ''}`}
              onClick={() => setActiveFilter('ambitious')}
            >
              Ambitious
            </button>
          </div>
        </div>
      </section>
      
      {/* Products Grid */}
      <section className="collections-products">
        <div className="container">
          <div className="collections-products__grid">
            {filteredProducts.map(product => (
              <div className="product-card" key={product.id}>
                <div className="product-card__image-container">
                  <img src={product.image} alt={product.name} className="product-card__image" />
                  <div className="product-card__overlay">
                    <Link to={`/product/${product.id}`} className="product-card__btn">View Details</Link>
                  </div>
                  {product.isNew && <span className="product-card__badge product-card__badge--new">New</span>}
                  {product.isBestseller && <span className="product-card__badge product-card__badge--bestseller">Bestseller</span>}
                </div>
                <div className="product-card__info">
                  <h3 className="product-card__name">
                    <Link to={`/product/${product.id}`} className="product-card__name-link">
                      {product.name}
                    </Link>
                  </h3>
                  <p className="product-card__collection">{product.collection}</p>
                  <p className="product-card__description">{product.description}</p>
                  <div className="product-card__bottom">
                    <span className="product-card__price">${product.price}</span>
                    <div className="product-card__sizes">
                      {product.sizes.map(size => (
                        <span key={size} className="product-card__size">{size}</span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Newsletter Section */}
      <section className="collections-newsletter">
        <div className="container">
          <div className="collections-newsletter__content">
            <h2 className="collections-newsletter__title">Join Our Narrative</h2>
            <p className="collections-newsletter__text">
              Subscribe to receive updates on new collections, stories behind our designs, and exclusive offers.
            </p>
            <form className="collections-newsletter__form">
              <input 
                type="email" 
                placeholder="Your email address" 
                className="collections-newsletter__input" 
                required 
              />
              <button type="submit" className="collections-newsletter__button">Subscribe</button>
            </form>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Collections;


