import { CONFIG } from '../config';

// WebSocket message types
export enum MessageType {
  // User management
  USER_JOIN = 'user_join',
  USER_LEAVE = 'user_leave',
  USER_LIST = 'user_list',
  USER_KICKED = 'user_kicked',
  
  // Chat messages
  CHAT_MESSAGE = 'chat_message',
  TYPING_START = 'typing_start',
  TYPING_STOP = 'typing_stop',
  
  // Voice call signaling
  CALL_START = 'call_start',
  CALL_END = 'call_end',
  CALL_JOIN = 'call_join',
  CALL_LEAVE = 'call_leave',
  
  // WebRTC signaling
  OFFER = 'offer',
  ANSWER = 'answer',
  ICE_CANDIDATE = 'ice_candidate',
  
  // System messages
  ERROR = 'error',
  PING = 'ping',
  PONG = 'pong'
}

// User interface
export interface ChatUser {
  id: string;
  username: string;
  isAdmin: boolean;
  isInCall: boolean;
  isMuted: boolean;
  joinedAt: Date;
}

// Message interfaces
export interface ChatMessage {
  id: string;
  userId: string;
  username: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'system';
}

export interface WebSocketMessage {
  type: MessageType;
  payload: any;
  timestamp: Date;
  userId?: string;
}

// WebSocket service class
export class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private pingInterval: NodeJS.Timeout | null = null;
  private listeners: Map<MessageType, Set<Function>> = new Map();
  private isConnecting = false;

  constructor(private roomId: string, private userId: string, private username: string) {
    this.initializeListeners();
  }

  private initializeListeners() {
    Object.values(MessageType).forEach(type => {
      this.listeners.set(type, new Set());
    });
  }

  // Connect to WebSocket server
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
        resolve();
        return;
      }

      this.isConnecting = true;
      const wsUrl = CONFIG.API.BASE_URL.replace('http', 'ws') + `/ws/chat/${this.roomId}`;
      
      try {
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.startPing();
          
          // Send join message
          this.send(MessageType.USER_JOIN, {
            userId: this.userId,
            username: this.username
          });
          
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.stopPing();
          
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  // Disconnect from WebSocket
  disconnect() {
    if (this.ws) {
      this.send(MessageType.USER_LEAVE, { userId: this.userId });
      this.ws.close(1000, 'User disconnected');
      this.ws = null;
    }
    this.stopPing();
  }

  // Send message through WebSocket
  send(type: MessageType, payload: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message: WebSocketMessage = {
        type,
        payload,
        timestamp: new Date(),
        userId: this.userId
      };
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }

  // Handle incoming messages
  private handleMessage(message: WebSocketMessage) {
    const listeners = this.listeners.get(message.type);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(message.payload, message);
        } catch (error) {
          console.error('Error in message listener:', error);
        }
      });
    }
  }

  // Add event listener
  on(type: MessageType, callback: Function) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.add(callback);
    }
  }

  // Remove event listener
  off(type: MessageType, callback: Function) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  // Reconnect logic
  private reconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  // Ping/pong for connection health
  private startPing() {
    this.pingInterval = setInterval(() => {
      this.send(MessageType.PING, {});
    }, 30000); // Ping every 30 seconds
  }

  private stopPing() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  // Get connection status
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  // Chat methods
  sendChatMessage(content: string) {
    this.send(MessageType.CHAT_MESSAGE, {
      content,
      username: this.username
    });
  }

  startTyping() {
    this.send(MessageType.TYPING_START, { username: this.username });
  }

  stopTyping() {
    this.send(MessageType.TYPING_STOP, { username: this.username });
  }

  // Call methods
  startCall() {
    this.send(MessageType.CALL_START, { userId: this.userId });
  }

  endCall() {
    this.send(MessageType.CALL_END, { userId: this.userId });
  }

  joinCall() {
    this.send(MessageType.CALL_JOIN, { userId: this.userId });
  }

  leaveCall() {
    this.send(MessageType.CALL_LEAVE, { userId: this.userId });
  }

  // WebRTC signaling methods
  sendOffer(targetUserId: string, offer: RTCSessionDescriptionInit) {
    this.send(MessageType.OFFER, {
      targetUserId,
      offer
    });
  }

  sendAnswer(targetUserId: string, answer: RTCSessionDescriptionInit) {
    this.send(MessageType.ANSWER, {
      targetUserId,
      answer
    });
  }

  sendIceCandidate(targetUserId: string, candidate: RTCIceCandidate) {
    this.send(MessageType.ICE_CANDIDATE, {
      targetUserId,
      candidate
    });
  }

  // Admin methods
  kickUser(targetUserId: string) {
    this.send(MessageType.USER_KICKED, {
      targetUserId,
      adminId: this.userId
    });
  }
}

export default WebSocketService;
