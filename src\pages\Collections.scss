@use '../styles/variables' as *;
@use '../styles/mixins' as *;
@use '../styles/common';

.collections-page {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

// Hero Section
.collections-hero {
  @extend .hero-section;
  min-height: 40vh;
  
  &::before {
    background-image: url('../assets/images/hero-bg.png');
  }
  
  &__content {
    @extend .hero-section__content;
    @include fade-in(0.8s);
  }
  
  &__title {
    @extend .hero-section__title;
    @include slide-in('up', 30px, 0.8s);
  }
  
  &__subtitle {
    @extend .hero-section__subtitle;
    @include slide-in('up', 20px, 1s);
  }
}

// Filters Section
.collections-filters {
  padding: $spacing-lg 0;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  
  &__wrapper {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: $spacing-md;
  }
  
  &__btn {
    background: none;
    border: none;
    padding: $spacing-sm $spacing-md;
    font-family: $font-primary;
    font-size: 1rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 2px;
      background-color: var(--accent-primary);
      transition: width 0.3s ease;
    }
    
    &:hover, &.active {
      color: var(--text-primary);
      
      &::after {
        width: 80%;
      }
    }
    
    &.active {
      font-weight: 600;
    }
  }
}

// Products Grid
.collections-products {
  padding: $spacing-xl 0;
  
  &__grid {
    @extend .grid;
    @extend .grid--3col;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    
    // Ensure grid maintains layout during and after animations
    & > * {
      min-height: 0; // Prevent grid items from expanding
      height: 100%;
      display: flex;
    }
  }
}

// Fix for Framer Motion animations in grid
.product-card-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Product Card
.product-card {
  @extend .card;
  
  &__image-container {
    position: relative;
    overflow: hidden;
    aspect-ratio: 3/4;
  }
  
  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }
  
  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover {
    .product-card__overlay {
      opacity: 1;
    }
    
    .product-card__image {
      transform: scale(1.05);
    }
  }
  
  &__btn {
    @extend .btn;
    @extend .btn-primary;
    padding: $spacing-sm $spacing-lg;
  }
  
  &__badge {
    position: absolute;
    top: $spacing-sm;
    left: $spacing-sm;
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 4px;
    z-index: 1;
    
    &--new {
      @extend .badge--primary;
    }
    
    &--bestseller {
      @extend .badge--danger;
    }
  }
  
  &__info {
    padding: $spacing-md;
  }
  
  &__name {
    @extend .card__title;
    margin-bottom: $spacing-xs;
    
    &-link {
      color: var(--text-primary);
      text-decoration: none;
      transition: color 0.3s ease;
      
      &:hover {
        color: var(--accent-primary);
      }
    }
  }
  
  &__collection {
    font-size: 0.875rem;
    color: var(--accent-primary);
    text-transform: capitalize;
    margin-bottom: $spacing-xs;
  }
  
  &__description {
    @extend .card__text;
  }
  
  &__bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  &__price {
    font-weight: 600;
    font-size: 1.125rem;
    color: var(--text-primary);
  }
  
  &__sizes {
    display: flex;
    gap: 5px;
  }
  
  &__size {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    font-size: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-secondary);
  }
}

// Newsletter Section
.collections-newsletter {
  padding: $spacing-xl 0;
  background-color: var(--accent-primary);
  color: white;
  
  &__content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
  }
  
  &__title {
    font-family: $font-serif;
    font-size: 2.5rem;
    margin-bottom: $spacing-md;
    
    @include mobile {
      font-size: 2rem;
    }
  }
  
  &__text {
    font-size: 1.125rem;
    margin-bottom: $spacing-lg;
    opacity: 0.9;
  }
  
  &__form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    
    @include mobile {
      flex-direction: column;
      gap: $spacing-sm;
    }
  }
  
  &__input {
    @extend .form__input;
    flex: 1;
    border-radius: 4px 0 0 4px;
    border: none;
    
    @include mobile {
      border-radius: 4px;
    }
  }
  
  &__button {
    padding: $spacing-sm $spacing-lg;
    background-color: var(--bg-primary);
    color: var(--accent-primary);
    border: none;
    border-radius: 0 4px 4px 0;
    font-family: $font-primary;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    
    @include mobile {
      border-radius: 4px;
    }
    
    &:hover {
      background-color: darken(white, 10%);
    }
  }
}



