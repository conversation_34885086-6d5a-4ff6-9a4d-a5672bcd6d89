// Mock WebSocket service that works entirely in the browser
// Uses localStorage and BroadcastChannel for cross-tab communication

export enum MessageType {
  USER_JOIN = 'user_join',
  USER_LEAVE = 'user_leave',
  USER_LIST = 'user_list',
  CALL_START = 'call_start',
  CALL_END = 'call_end',
  CALL_JOIN = 'call_join',
  CALL_LEAVE = 'call_leave',
  OFFER = 'offer',
  ANSWER = 'answer',
  ICE_CANDIDATE = 'ice_candidate',
  ERROR = 'error'
}

export interface ChatUser {
  id: string;
  username: string;
  isAdmin: boolean;
  isInCall: boolean;
  isMuted: boolean;
  joinedAt: Date;
}

export interface WebSocketMessage {
  type: MessageType;
  payload: any;
  timestamp: Date;
  userId?: string;
}

export class MockWebSocketService {
  private channel: BroadcastChannel;
  private listeners: Map<MessageType, Set<Function>> = new Map();
  private isConnected = false;
  private roomId: string;
  private userId: string;
  private username: string;
  private currentUser: ChatUser | null = null;

  constructor(roomId: string, userId: string, username: string) {
    this.roomId = roomId;
    this.userId = userId;
    this.username = username;
    this.channel = new BroadcastChannel(`chat_${roomId}`);
    this.initializeListeners();
    this.setupChannelListener();
  }

  private initializeListeners() {
    Object.values(MessageType).forEach(type => {
      this.listeners.set(type, new Set());
    });
  }

  private setupChannelListener() {
    this.channel.onmessage = (event) => {
      const message: WebSocketMessage = event.data;
      
      // Don't process our own messages
      if (message.userId === this.userId) return;
      
      this.handleMessage(message);
    };
  }

  // Connect to the mock WebSocket
  connect(): Promise<void> {
    return new Promise((resolve) => {
      this.isConnected = true;
      
      // Create current user
      this.currentUser = {
        id: this.userId,
        username: this.username,
        isAdmin: false,
        isInCall: false,
        isMuted: false,
        joinedAt: new Date()
      };

      // Add user to localStorage
      this.addUserToRoom(this.currentUser);
      
      // Broadcast join message
      this.send(MessageType.USER_JOIN, this.currentUser);
      
      // Send current user list to new user
      setTimeout(() => {
        const users = this.getUsersInRoom();
        this.emit(MessageType.USER_LIST, users);
      }, 100);
      
      resolve();
    });
  }

  // Disconnect from mock WebSocket
  disconnect() {
    if (this.isConnected && this.currentUser) {
      this.removeUserFromRoom(this.userId);
      this.send(MessageType.USER_LEAVE, { 
        userId: this.userId, 
        username: this.username 
      });
    }
    
    this.isConnected = false;
    this.channel.close();
  }

  // Send message through BroadcastChannel
  send(type: MessageType, payload: any) {
    if (this.isConnected) {
      const message: WebSocketMessage = {
        type,
        payload,
        timestamp: new Date(),
        userId: this.userId
      };
      
      this.channel.postMessage(message);
    }
  }

  // Handle incoming messages
  private handleMessage(message: WebSocketMessage) {
    // Handle user management messages
    if (message.type === MessageType.USER_JOIN) {
      this.addUserToRoom(message.payload);
      this.emit(MessageType.USER_JOIN, message.payload);
      
      // Send updated user list
      const users = this.getUsersInRoom();
      this.emit(MessageType.USER_LIST, users);
    } else if (message.type === MessageType.USER_LEAVE) {
      this.removeUserFromRoom(message.payload.userId);
      this.emit(MessageType.USER_LEAVE, message.payload);
      
      // Send updated user list
      const users = this.getUsersInRoom();
      this.emit(MessageType.USER_LIST, users);
    } else {
      // Forward other messages to listeners
      this.emit(message.type, message.payload);
    }
  }

  // Add event listener
  on(type: MessageType, callback: Function) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.add(callback);
    }
  }

  // Remove event listener
  off(type: MessageType, callback: Function) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  // Emit event to listeners
  private emit(type: MessageType, payload: any) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(payload);
        } catch (error) {
          console.error('Error in message listener:', error);
        }
      });
    }
  }

  // User management with localStorage
  private addUserToRoom(user: ChatUser) {
    const users = this.getUsersInRoom();
    const existingIndex = users.findIndex(u => u.id === user.id);
    
    if (existingIndex >= 0) {
      users[existingIndex] = user;
    } else {
      users.push(user);
    }
    
    localStorage.setItem(`chat_users_${this.roomId}`, JSON.stringify(users));
  }

  private removeUserFromRoom(userId: string) {
    const users = this.getUsersInRoom();
    const filteredUsers = users.filter(u => u.id !== userId);
    localStorage.setItem(`chat_users_${this.roomId}`, JSON.stringify(filteredUsers));
  }

  private getUsersInRoom(): ChatUser[] {
    const usersJson = localStorage.getItem(`chat_users_${this.roomId}`);
    if (!usersJson) return [];
    
    try {
      const users = JSON.parse(usersJson);
      // Clean up old users (older than 5 minutes)
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      const activeUsers = users.filter((user: ChatUser) => 
        new Date(user.joinedAt) > fiveMinutesAgo
      );
      
      if (activeUsers.length !== users.length) {
        localStorage.setItem(`chat_users_${this.roomId}`, JSON.stringify(activeUsers));
      }
      
      return activeUsers;
    } catch {
      return [];
    }
  }

  // Voice call methods
  startCall() {
    this.send(MessageType.CALL_START, { userId: this.userId });
  }

  endCall() {
    this.send(MessageType.CALL_END, { userId: this.userId });
  }

  joinCall() {
    this.send(MessageType.CALL_JOIN, { userId: this.userId });
  }

  leaveCall() {
    this.send(MessageType.CALL_LEAVE, { userId: this.userId });
  }

  // WebRTC signaling methods
  sendOffer(targetUserId: string, offer: RTCSessionDescriptionInit) {
    this.send(MessageType.OFFER, {
      targetUserId,
      offer,
      fromUserId: this.userId
    });
  }

  sendAnswer(targetUserId: string, answer: RTCSessionDescriptionInit) {
    this.send(MessageType.ANSWER, {
      targetUserId,
      answer,
      fromUserId: this.userId
    });
  }

  sendIceCandidate(targetUserId: string, candidate: RTCIceCandidate) {
    this.send(MessageType.ICE_CANDIDATE, {
      targetUserId,
      candidate,
      fromUserId: this.userId
    });
  }

  // Get connection status
  get isConnectedStatus(): boolean {
    return this.isConnected;
  }

  // Update user call status
  updateUserCallStatus(isInCall: boolean, isMuted: boolean = false) {
    if (this.currentUser) {
      this.currentUser.isInCall = isInCall;
      this.currentUser.isMuted = isMuted;
      this.addUserToRoom(this.currentUser);
      
      // Broadcast updated user info
      this.send(MessageType.USER_JOIN, this.currentUser);
    }
  }
}

export default MockWebSocketService;
