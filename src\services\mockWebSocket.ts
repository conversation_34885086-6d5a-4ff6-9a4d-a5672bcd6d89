// Mock WebSocket service that works entirely in the browser
// Uses localStorage and BroadcastChannel for cross-tab communication

export enum MessageType {
  USER_JOIN = 'user_join',
  USER_LEAVE = 'user_leave',
  USER_LIST = 'user_list',
  CALL_START = 'call_start',
  CALL_END = 'call_end',
  CALL_JOIN = 'call_join',
  CALL_LEAVE = 'call_leave',
  OFFER = 'offer',
  ANSWER = 'answer',
  ICE_CANDIDATE = 'ice_candidate',
  ERROR = 'error'
}

export interface ChatUser {
  id: string;
  username: string;
  isAdmin: boolean;
  isInCall: boolean;
  isMuted: boolean;
  joinedAt: Date;
}

export interface WebSocketMessage {
  type: MessageType;
  payload: any;
  timestamp: Date;
  userId?: string;
}

export class MockWebSocketService {
  private channel: BroadcastChannel;
  private listeners: Map<MessageType, Set<Function>> = new Map();
  private isConnected = false;
  private roomId: string;
  private userId: string;
  private username: string;
  private currentUser: ChatUser | null = null;
  private syncInterval: NodeJS.Timeout | null = null;

  constructor(roomId: string, userId: string, username: string) {
    this.roomId = roomId;
    this.userId = userId;
    this.username = username;
    this.channel = new BroadcastChannel(`chat_${roomId}`);
    this.initializeListeners();
    this.setupChannelListener();
  }

  private initializeListeners() {
    Object.values(MessageType).forEach(type => {
      this.listeners.set(type, new Set());
    });
  }

  private setupChannelListener() {
    this.channel.onmessage = (event) => {
      const message: WebSocketMessage = event.data;

      // Don't process our own messages for most events, but allow some for debugging
      if (message.userId === this.userId &&
        message.type !== MessageType.USER_LIST &&
        message.type !== MessageType.CALL_START &&
        message.type !== MessageType.CALL_JOIN) {
        return;
      }

      console.log('Received message:', message.type, message.payload);
      this.handleMessage(message);
    };
  }

  // Connect to the mock WebSocket
  connect(): Promise<void> {
    return new Promise((resolve) => {
      this.isConnected = true;

      // Create current user
      this.currentUser = {
        id: this.userId,
        username: this.username,
        isAdmin: false,
        isInCall: false,
        isMuted: false,
        joinedAt: new Date()
      };

      // Add user to localStorage first
      this.addUserToRoom(this.currentUser);

      // Get initial user list
      const initialUsers = this.getUsersInRoom();
      console.log('Initial users on connect:', initialUsers);

      // Emit initial user list immediately
      this.emit(MessageType.USER_LIST, initialUsers);

      // Broadcast join message to other tabs
      this.send(MessageType.USER_JOIN, this.currentUser);

      // Set up periodic user list sync
      this.startUserListSync();

      resolve();
    });
  }

  // Start periodic user list synchronization
  private startUserListSync() {
    // Sync user list every 2 seconds
    this.syncInterval = setInterval(() => {
      if (this.isConnected) {
        const users = this.getUsersInRoom();
        this.emit(MessageType.USER_LIST, users);
      }
    }, 2000);
  }

  // Disconnect from mock WebSocket
  disconnect() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    if (this.isConnected && this.currentUser) {
      this.removeUserFromRoom(this.userId);
      this.send(MessageType.USER_LEAVE, {
        userId: this.userId,
        username: this.username
      });
    }

    this.isConnected = false;
    this.channel.close();
  }

  // Send message through BroadcastChannel
  send(type: MessageType, payload: any) {
    if (this.isConnected) {
      const message: WebSocketMessage = {
        type,
        payload,
        timestamp: new Date(),
        userId: this.userId
      };

      console.log('Sending message:', type, payload);
      this.channel.postMessage(message);

      // Also emit to our own listeners for immediate feedback
      if (type === MessageType.USER_JOIN || type === MessageType.CALL_JOIN) {
        setTimeout(() => this.handleMessage(message), 50);
      }
    }
  }

  // Handle incoming messages
  private handleMessage(message: WebSocketMessage) {
    console.log('Handling message:', message.type, message.payload);

    // Handle user management messages
    if (message.type === MessageType.USER_JOIN) {
      this.addUserToRoom(message.payload);
      this.emit(MessageType.USER_JOIN, message.payload);

      // Send updated user list to all listeners
      setTimeout(() => {
        const users = this.getUsersInRoom();
        console.log('Broadcasting user list:', users);
        this.emit(MessageType.USER_LIST, users);
      }, 100);

    } else if (message.type === MessageType.USER_LEAVE) {
      this.removeUserFromRoom(message.payload.userId);
      this.emit(MessageType.USER_LEAVE, message.payload);

      // Send updated user list
      setTimeout(() => {
        const users = this.getUsersInRoom();
        this.emit(MessageType.USER_LIST, users);
      }, 100);

    } else {
      // Forward other messages to listeners
      this.emit(message.type, message.payload);
    }
  }

  // Add event listener
  on(type: MessageType, callback: Function) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.add(callback);
    }
  }

  // Remove event listener
  off(type: MessageType, callback: Function) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  // Emit event to listeners
  private emit(type: MessageType, payload: any) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(payload);
        } catch (error) {
          console.error('Error in message listener:', error);
        }
      });
    }
  }

  // User management with localStorage
  private addUserToRoom(user: ChatUser) {
    const users = this.getUsersInRoom();
    const existingIndex = users.findIndex(u => u.id === user.id);

    if (existingIndex >= 0) {
      users[existingIndex] = user;
    } else {
      users.push(user);
    }

    localStorage.setItem(`chat_users_${this.roomId}`, JSON.stringify(users));
  }

  private removeUserFromRoom(userId: string) {
    const users = this.getUsersInRoom();
    const filteredUsers = users.filter(u => u.id !== userId);
    localStorage.setItem(`chat_users_${this.roomId}`, JSON.stringify(filteredUsers));
  }

  private getUsersInRoom(): ChatUser[] {
    const usersJson = localStorage.getItem(`chat_users_${this.roomId}`);
    if (!usersJson) return [];

    try {
      const users = JSON.parse(usersJson);
      // Clean up old users (older than 2 minutes for faster cleanup)
      const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000);
      const activeUsers = users.filter((user: ChatUser) => {
        const userJoinedAt = new Date(user.joinedAt);
        return userJoinedAt > twoMinutesAgo;
      });

      // Update localStorage if we cleaned up users
      if (activeUsers.length !== users.length) {
        localStorage.setItem(`chat_users_${this.roomId}`, JSON.stringify(activeUsers));
        console.log('Cleaned up old users, active users:', activeUsers.length);
      }

      return activeUsers;
    } catch (error) {
      console.error('Error parsing users from localStorage:', error);
      return [];
    }
  }

  // Voice call methods
  startCall() {
    this.send(MessageType.CALL_START, { userId: this.userId });
  }

  endCall() {
    this.send(MessageType.CALL_END, { userId: this.userId });
  }

  joinCall() {
    this.send(MessageType.CALL_JOIN, { userId: this.userId });
  }

  leaveCall() {
    this.send(MessageType.CALL_LEAVE, { userId: this.userId });
  }

  // WebRTC signaling methods
  sendOffer(targetUserId: string, offer: RTCSessionDescriptionInit) {
    this.send(MessageType.OFFER, {
      targetUserId,
      offer,
      fromUserId: this.userId
    });
  }

  sendAnswer(targetUserId: string, answer: RTCSessionDescriptionInit) {
    this.send(MessageType.ANSWER, {
      targetUserId,
      answer,
      fromUserId: this.userId
    });
  }

  sendIceCandidate(targetUserId: string, candidate: RTCIceCandidate) {
    this.send(MessageType.ICE_CANDIDATE, {
      targetUserId,
      candidate,
      fromUserId: this.userId
    });
  }

  // Get connection status
  get isConnectedStatus(): boolean {
    return this.isConnected;
  }

  // Update user call status
  updateUserCallStatus(isInCall: boolean, isMuted: boolean = false) {
    if (this.currentUser) {
      this.currentUser.isInCall = isInCall;
      this.currentUser.isMuted = isMuted;
      this.currentUser.joinedAt = new Date(); // Update timestamp to keep user active

      this.addUserToRoom(this.currentUser);

      // Broadcast updated user info
      this.send(MessageType.USER_JOIN, this.currentUser);

      console.log('Updated user call status:', {
        userId: this.userId,
        isInCall,
        isMuted
      });
    }
  }
}

export default MockWebSocketService;
