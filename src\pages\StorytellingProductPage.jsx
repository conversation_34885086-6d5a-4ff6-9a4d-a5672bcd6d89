import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import StorytellingProductPage from '../components/product/StorytellingProductPage';

// Import default product image
import defaultProductImage from '../assets/images/product.png';

const StorytellingProductView = () => {
  const { id } = useParams();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);

  // Mock product data with storytelling elements
  const mockProducts = [
    {
      id: "1",
      name: "The Prometheus Tee",
      collection: "Rebellion",
      price: 89.99,
      image: defaultProductImage,
      isNew: true,
      story: "Inspired by the titan who defied the gods to bring fire to humanity. This design captures the essence of rebellion against established order, the courage to challenge authority for the greater good. The flame motif represents knowledge, enlightenment, and the spark of creativity that drives human progress.",
      outcome: {
        title: "What This Symbolizes",
        keywords: ["Defiance", "Enlightenment", "Sacrifice", "Progress"]
      },
      compliment: {
        text: "Wear this as a reminder that true progress often requires challenging the status quo.",
        symbol: "flame"
      },
      dropTheme: "Titans"
    },
    {
      id: "2",
      name: "Ouroboros Sweater",
      collection: "Eternal Cycle",
      price: 129.99,
      image: defaultProductImage,
      isNew: false,
      story: "The ancient symbol of the serpent consuming its own tail represents the eternal cycle of creation and destruction. This garment embodies the concept of perpetual renewal and the cyclical nature of existence. The intricate pattern weaves together beginning and end in perfect harmony.",
      outcome: {
        title: "What This Symbolizes",
        keywords: ["Renewal", "Infinity", "Self-Reflection", "Wholeness"]
      },
      compliment: {
        text: "A reminder that endings are merely new beginnings in disguise.",
        symbol: "circle"
      },
      dropTheme: "Eternal Symbols"
    },
    {
      id: "3",
      name: "Oracle's Vision",
      collection: "Oracle",
      price: 94.99,
      image: defaultProductImage,
      isNew: true,
      story: "Inspired by the ancient oracles who could perceive what others could not. The subtle pattern embedded in the fabric reveals different aspects depending on the light, symbolizing the changing nature of truth and perception. This piece connects the wearer to intuitive wisdom beyond rational thought.",
      outcome: {
        title: "What This Embodies",
        keywords: ["Intuition", "Foresight", "Hidden Knowledge", "Wisdom"]
      },
      compliment: {
        text: "Look beyond the surface to find the deeper truths that guide your path.",
        symbol: "eye"
      },
      dropTheme: "Mystic Seers"
    }
  ];

  useEffect(() => {
    // Simulate API fetch
    setTimeout(() => {
      const foundProduct = mockProducts.find(p => p.id === id);
      setProduct(foundProduct || null);
      setLoading(false);
    }, 500);
  }, [id]);

  if (loading) {
    return (
      <div className="container">
        <div className="loading">Loading product...</div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container">
        <div className="not-found">
          <h2>Product Not Found</h2>
          <p>The product you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    );
  }

  return <StorytellingProductPage product={product} />;
};

export default StorytellingProductView;
