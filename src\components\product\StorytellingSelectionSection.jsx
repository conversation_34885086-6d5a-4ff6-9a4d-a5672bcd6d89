import React, { useState } from 'react';
import { motion } from 'framer-motion';
import SizeSelector from './SizeSelector';
import ColorSelector from './ColorSelector';
import SizeGuide from './SizeGuide';

const StorytellingSelectionSection = ({ 
  product, 
  selectedSize, 
  setSelectedSize, 
  selectedColor, 
  handleColorSelect, 
  handleImageChange, 
  isInView 
}) => {
  const [isSizeGuideOpen, setIsSizeGuideOpen] = useState(false);
  
  const openSizeGuide = () => {
    setIsSizeGuideOpen(true);
  };
  
  const closeSizeGuide = () => {
    setIsSizeGuideOpen(false);
  };
  
  return (
    <motion.div 
      className="storytelling-product__selection-section"
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: 0.4 }}
    >
      <div className="storytelling-product__selection-container">
        {/* Color Selector - No Label */}
        {product.colors && product.colors.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="storytelling-product__color-selector"
          >
            <ColorSelector 
              colors={product.colors}
              selectedColor={selectedColor}
              onColorSelect={handleColorSelect}
              onImageChange={handleImageChange}
              hideLabel={true}
            />
          </motion.div>
        )}
        
        {/* Size Selector - No Label */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          className="storytelling-product__size-selector"
        >
          <SizeSelector 
            sizes={product.sizes}
            selectedSize={selectedSize}
            onSizeSelect={setSelectedSize}
            hideLabel={true}
          />
        </motion.div>
        
        {/* Size Guide Button */}
        <motion.button
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="storytelling-product__size-guide-btn"
          onClick={openSizeGuide}
        >
          Size Guide
        </motion.button>
        
        {/* Add to Cart Button */}
        <motion.button
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.9 }}
          className={`storytelling-product__add-to-cart ${!selectedSize ? 'storytelling-product__add-to-cart--disabled' : ''}`}
          disabled={!selectedSize}
        >
          {selectedSize ? 'Add to Cart' : 'Select a Size'}
        </motion.button>
      </div>
      
      {/* Size Guide Modal */}
      <SizeGuide isOpen={isSizeGuideOpen} onClose={closeSizeGuide} />
    </motion.div>
  );
};

export default StorytellingSelectionSection;