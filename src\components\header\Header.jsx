import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import Cookies from 'js-cookie';
import UserAvatar from '../common/UserAvatar';
import { useAuth } from '../../hooks/useAuth';
import './Header.scss';

// Cookie names (kept for backward compatibility)
const USER_COOKIE_NAME = 'wolzyn_user';
const TOKEN_COOKIE_NAME = 'wolzyn_token';

const Header = () => {
  const [scrolled, setScrolled] = useState(false);
  const [prevScrollPos, setPrevScrollPos] = useState(0);
  const [visible, setVisible] = useState(true);
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const navigate = useNavigate();

  // Use the auth hook for user management
  const { user, isLoading, logout: authLogout, fetchProfile, error } = useAuth();

  // Fetch profile on mount if user is authenticated but profile is missing
  useEffect(() => {
    if (!user && !isLoading && !error) {
      fetchProfile();
    }
  }, [user, isLoading, error, fetchProfile]);

  // Handle scroll behavior
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollPos = window.scrollY;

      // Determine if header should be visible based on scroll direction
      setVisible(prevScrollPos > currentScrollPos || currentScrollPos < 50);
      setPrevScrollPos(currentScrollPos);

      // Add background when scrolled
      setScrolled(currentScrollPos > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [prevScrollPos]);

  const toggleMobileNav = () => setMobileNavOpen(!mobileNavOpen);
  const toggleUserMenu = () => setUserMenuOpen(!userMenuOpen);

  const handleSignOut = async () => {
    try {
      setUserMenuOpen(false);
      await authLogout();
    } catch (error) {
      console.error('Sign out error:', error);
      // Fallback: clear local data even if backend call fails
      Cookies.remove(USER_COOKIE_NAME);
      Cookies.remove(TOKEN_COOKIE_NAME);
      navigate('/');
    }
  };

  return (
    <header
      className={`header ${scrolled ? 'header--scrolled' : ''} ${!visible ? 'header--hidden' : ''}`}
    >
      <div className="container header__container">
        <motion.div
          className="header__logo-container"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1.2, ease: [0.16, 1, 0.3, 1] }}
        >
          <Link to="/" className="header__logo">
            <span className='icon-Studio-Project-1 icon-studio'></span>
            <span className="header__logo-text">WolZyn</span>
          </Link>
        </motion.div>

        <nav className="header__nav desktop-nav">
          <motion.ul
            className="desktop-nav__list"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            {['Home', 'Collections', 'Story', 'About', 'Chat', 'Cart'].map((item, index) => (
              <motion.li
                key={item}
                className="desktop-nav__item"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 + (index * 0.1) }}
              >
                <Link
                  to={item === 'Home' ? '/' :
                    item === 'Chat' ? '/chat-rooms' :
                      `/${item.toLowerCase()}`}
                  className="desktop-nav__link"
                >
                  {item}
                </Link>
              </motion.li>
            ))}

            {/* Sign In Link - Only show if user is not logged in */}
            {!user && (
              <motion.li
                className="desktop-nav__item"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.9 }}
              >
                <Link
                  to="/signup"
                  className="desktop-nav__link"
                >
                  Sign In
                </Link>
              </motion.li>
            )}
          </motion.ul>
        </nav>

        {/* User Account Icon */}
        {user && (
          <div className="header__user-container">
            <div className="user-menu-container">
              <UserAvatar
                user={user}
                size="medium"
                onClick={toggleUserMenu}
                className="user-icon-button"
                alt="User menu"
              />

              <AnimatePresence>
                {userMenuOpen && (
                  <motion.div
                    className="user-menu"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="user-menu__header">
                      <p className="user-menu__name">
                        {user.name || 'WolZyn User'}
                      </p>
                      <p className="user-menu__email">
                        {user.email || ''}
                      </p>
                    </div>
                    <div className="user-menu__divider"></div>
                    <ul className="user-menu__list">
                      <li className="user-menu__item">
                        <Link to="/profile" className="user-menu__link">
                          Profile
                        </Link>
                      </li>
                      <li className="user-menu__item">
                        <Link to="/orders" className="user-menu__link">
                          Orders
                        </Link>
                      </li>
                      <li className="user-menu__item">
                        <button
                          className="user-menu__button user-menu__button--sign-out"
                          onClick={handleSignOut}
                        >
                          Sign Out
                        </button>
                      </li>
                    </ul>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        )}

        {/* Mobile Navigation Trigger */}
        <div className="mobile-nav-trigger" onClick={toggleMobileNav}>
          <span className="mobile-nav-trigger__text">
            {mobileNavOpen ? 'Close' : 'Menu'}
          </span>
        </div>
      </div>

      {/* Mobile Navigation Panel */}
      <AnimatePresence>
        {mobileNavOpen && (
          <motion.div
            className="mobile-nav"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            transition={{ duration: 0.5, ease: [0.16, 1, 0.3, 1] }}
          >
            <ul className="mobile-nav__list">
              {['Home', 'Collections', 'Story', 'About', 'Chat', 'Cart'].map((item, index) => (
                <motion.li
                  key={item}
                  className="mobile-nav__item"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 + (index * 0.1) }}
                >
                  <Link
                    to={item === 'Home' ? '/' :
                      item === 'Chat' ? '/chat-rooms' :
                        `/${item.toLowerCase()}`}
                    className="mobile-nav__link"
                    onClick={() => setMobileNavOpen(false)}
                  >
                    {item}
                  </Link>
                </motion.li>
              ))}

              {/* User Account in Mobile Menu or Sign In Link */}
              {user ? (
                <>
                  <motion.li
                    className="mobile-nav__item mobile-nav__item--user"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <div className="mobile-nav__user-info">
                      <UserAvatar
                        user={user}
                        size="large"
                        className="mobile-nav__user-avatar"
                        alt="User profile"
                      />
                      <div className="mobile-nav__user-details">
                        <p className="mobile-nav__user-name">
                          {user.name || 'WolZyn User'}
                        </p>
                        <p className="mobile-nav__user-email">
                          {user.email || ''}
                        </p>
                      </div>
                    </div>
                  </motion.li>
                  <motion.li
                    className="mobile-nav__item"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 }}
                  >
                    <Link
                      to="/profile"
                      className="mobile-nav__link"
                      onClick={() => setMobileNavOpen(false)}
                    >
                      Profile
                    </Link>
                  </motion.li>
                  <motion.li
                    className="mobile-nav__item"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                  >
                    <Link
                      to="/orders"
                      className="mobile-nav__link"
                      onClick={() => setMobileNavOpen(false)}
                    >
                      Orders
                    </Link>
                  </motion.li>
                  <motion.li
                    className="mobile-nav__item"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.9 }}
                  >
                    <button
                      className="mobile-nav__button mobile-nav__button--sign-out"
                      onClick={() => {
                        handleSignOut();
                        setMobileNavOpen(false);
                      }}
                    >
                      Sign Out
                    </button>
                  </motion.li>
                </>
              ) : (
                <motion.li
                  className="mobile-nav__item"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  <Link
                    to="/signup"
                    className="mobile-nav__link"
                    onClick={() => setMobileNavOpen(false)}
                  >
                    Sign In
                  </Link>
                </motion.li>
              )}
            </ul>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Header;



