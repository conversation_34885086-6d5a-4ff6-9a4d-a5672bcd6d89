import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import AgoraUIKit from 'agora-react-uikit';
import './AgoraChat.scss';

const AgoraChat: React.FC = () => {
  const navigate = useNavigate();
  const [username, setUsername] = useState('');
  const [isJoined, setIsJoined] = useState(false);
  const [showUsernameModal, setShowUsernameModal] = useState(true);
  const [channelName, setChannelName] = useState('gamers');
  const [videoCall, setVideoCall] = useState(false);

  // Get Agora App ID from environment
  const agoraAppId = import.meta.env.VITE_AGORA_APP_ID;

  // Check if Agora App ID is configured
  useEffect(() => {
    if (!agoraAppId) {
      console.error('Agora App ID not found. Please set VITE_AGORA_APP_ID in your .env file');
    }
  }, [agoraAppId]);

  // Join the voice chat
  const handleJoinChat = () => {
    if (!username.trim()) return;
    if (!agoraAppId) {
      alert('Agora App ID not configured. Please check your .env file.');
      return;
    }

    setIsJoined(true);
    setShowUsernameModal(false);
  };

  // Leave the voice chat
  const handleLeaveChat = () => {
    setIsJoined(false);
    setShowUsernameModal(true);
    setUsername('');
  };

  // Agora RTC Props
  const rtcProps = {
    appId: agoraAppId,
    channel: channelName,
    token: null, // For production, you should use a token server
    uid: null,
    layout: 1, // 0 for floating layout, 1 for grid layout
    enableScreensharing: false,
    enableVideo: videoCall,
    enableAudio: true,
  };

  // Agora callbacks
  const callbacks = {
    EndCall: () => {
      console.log('Call ended');
      handleLeaveChat();
    },
    'user-joined': (user: any) => {
      console.log('User joined:', user);
    },
    'user-left': (user: any) => {
      console.log('User left:', user);
    },
  };

  // Show error if no App ID
  if (!agoraAppId) {
    return (
      <div className="agora-chat">
        <div className="agora-chat__error">
          <h2>❌ Configuration Error</h2>
          <p>Agora App ID is not configured.</p>
          <div style={{ 
            background: '#f8f9fa', 
            padding: '1rem', 
            borderRadius: '8px',
            marginBottom: '1rem',
            fontFamily: 'monospace',
            fontSize: '0.875rem'
          }}>
            <p><strong>To fix this:</strong></p>
            <ol style={{ paddingLeft: '1.5rem' }}>
              <li>Copy <code>.env.example</code> to <code>.env</code></li>
              <li>Set <code>VITE_AGORA_APP_ID=b6cee8251e324457be191e8b1a0ef090</code></li>
              <li>Restart the development server</li>
            </ol>
          </div>
          <button onClick={() => navigate('/')}>Back to Home</button>
        </div>
      </div>
    );
  }

  return (
    <div className="agora-chat">
      {/* Username Modal */}
      <AnimatePresence>
        {showUsernameModal && (
          <motion.div
            className="agora-chat__modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="agora-chat__modal"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <h2>🎮 Join Agora Voice Chat</h2>
              <p>Professional voice chat powered by Agora</p>
              
              <form onSubmit={(e) => { e.preventDefault(); handleJoinChat(); }}>
                <div className="agora-chat__form-group">
                  <label>Username:</label>
                  <input
                    type="text"
                    placeholder="Enter your username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    maxLength={20}
                    autoFocus
                  />
                </div>

                <div className="agora-chat__form-group">
                  <label>Channel:</label>
                  <select 
                    value={channelName} 
                    onChange={(e) => setChannelName(e.target.value)}
                  >
                    <option value="gamers">🎮 Gamers</option>
                    <option value="general">💬 General</option>
                    <option value="music">🎵 Music</option>
                    <option value="study">📚 Study</option>
                  </select>
                </div>

                <div className="agora-chat__form-group">
                  <label className="agora-chat__checkbox">
                    <input
                      type="checkbox"
                      checked={videoCall}
                      onChange={(e) => setVideoCall(e.target.checked)}
                    />
                    Enable Video Call
                  </label>
                </div>

                <div className="agora-chat__modal-actions">
                  <button
                    type="button"
                    onClick={() => navigate('/')}
                    className="agora-chat__btn agora-chat__btn--secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={!username.trim()}
                    className="agora-chat__btn agora-chat__btn--primary"
                  >
                    Join Channel
                  </button>
                </div>
              </form>

              <div className="agora-chat__info">
                <h4>🚀 Powered by Agora</h4>
                <ul>
                  <li>✅ Professional voice quality</li>
                  <li>✅ Low latency communication</li>
                  <li>✅ Global infrastructure</li>
                  <li>✅ Scalable to thousands of users</li>
                </ul>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Agora Voice Chat Interface */}
      {isJoined && (
        <div className="agora-chat__container">
          <div className="agora-chat__header">
            <div className="agora-chat__user-info">
              <h2>🎮 {channelName.charAt(0).toUpperCase() + channelName.slice(1)} Channel</h2>
              <p>Welcome, <strong>{username}</strong>!</p>
            </div>
            <button
              onClick={handleLeaveChat}
              className="agora-chat__leave-btn"
            >
              📞 Leave Channel
            </button>
          </div>

          <div className="agora-chat__agora-container">
            <AgoraUIKit 
              rtcProps={rtcProps} 
              callbacks={callbacks}
              styleProps={{
                localBtnContainer: {
                  backgroundColor: '#007bff',
                  borderRadius: '8px',
                },
                maxViewStyles: {
                  height: '400px',
                  borderRadius: '12px',
                  overflow: 'hidden',
                },
                minViewStyles: {
                  borderRadius: '8px',
                  overflow: 'hidden',
                },
              }}
            />
          </div>

          <div className="agora-chat__controls">
            <div className="agora-chat__channel-info">
              <span>📡 Channel: <strong>{channelName}</strong></span>
              <span>🎤 Mode: {videoCall ? 'Video + Audio' : 'Audio Only'}</span>
            </div>
          </div>

          <div className="agora-chat__footer">
            <button
              onClick={() => navigate('/')}
              className="agora-chat__back-btn"
            >
              ← Back to Home
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgoraChat;
