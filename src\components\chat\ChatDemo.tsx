import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * Demo component showing how to integrate the chat system
 * This can be used as a reference for implementing chat features
 */
const ChatDemo: React.FC = () => {
  const navigate = useNavigate();
  const [selectedRoom, setSelectedRoom] = useState('general');

  const demoRooms = [
    { id: 'general', name: 'General Cha<PERSON>', users: 12 },
    { id: 'fashion', name: 'Fashion Talk', users: 8 },
    { id: 'support', name: 'Customer Support', users: 3 },
    { id: 'vip', name: 'VIP Lounge', users: 5 }
  ];

  const handleJoinRoom = (roomId: string) => {
    navigate(`/chat?room=${roomId}`);
  };

  const handleQuickJoin = () => {
    navigate(`/chat?room=${selectedRoom}`);
  };

  return (
    <div style={{ 
      padding: '2rem', 
      maxWidth: '800px', 
      margin: '0 auto',
      fontFamily: 'system-ui, sans-serif'
    }}>
      <h1>Chat System Demo</h1>
      <p>This demonstrates the chat and voice calling features of Wolzyn Apparels.</p>

      {/* Quick Join */}
      <div style={{ 
        background: '#f8f9fa', 
        padding: '1.5rem', 
        borderRadius: '8px', 
        marginBottom: '2rem' 
      }}>
        <h2>Quick Join</h2>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          <select 
            value={selectedRoom} 
            onChange={(e) => setSelectedRoom(e.target.value)}
            style={{ 
              padding: '0.5rem', 
              borderRadius: '4px', 
              border: '1px solid #ddd',
              minWidth: '150px'
            }}
          >
            {demoRooms.map(room => (
              <option key={room.id} value={room.id}>
                {room.name} ({room.users} users)
              </option>
            ))}
          </select>
          <button 
            onClick={handleQuickJoin}
            style={{
              padding: '0.5rem 1rem',
              background: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Join Chat
          </button>
        </div>
      </div>

      {/* Available Rooms */}
      <div style={{ marginBottom: '2rem' }}>
        <h2>Available Chat Rooms</h2>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
          gap: '1rem' 
        }}>
          {demoRooms.map(room => (
            <div 
              key={room.id}
              style={{
                border: '1px solid #ddd',
                borderRadius: '8px',
                padding: '1rem',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                background: 'white'
              }}
              onClick={() => handleJoinRoom(room.id)}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = '#007bff';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = '#ddd';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <h3 style={{ margin: '0 0 0.5rem 0', color: '#333' }}>
                #{room.name}
              </h3>
              <p style={{ margin: '0', color: '#666', fontSize: '0.9rem' }}>
                {room.users} users online
              </p>
              <div style={{ 
                marginTop: '1rem', 
                padding: '0.5rem 1rem', 
                background: '#007bff', 
                color: 'white', 
                borderRadius: '4px', 
                textAlign: 'center',
                fontSize: '0.9rem'
              }}>
                Join Room
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Features Overview */}
      <div style={{ marginBottom: '2rem' }}>
        <h2>Chat Features</h2>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: '1rem' 
        }}>
          {[
            { icon: '💬', title: 'Real-time Chat', desc: 'Instant messaging with typing indicators' },
            { icon: '🎤', title: 'Voice Calls', desc: 'High-quality voice communication' },
            { icon: '👥', title: 'User Management', desc: 'See who\'s online and manage users' },
            { icon: '🔒', title: 'Secure', desc: 'End-to-end encrypted communications' }
          ].map((feature, index) => (
            <div 
              key={index}
              style={{
                textAlign: 'center',
                padding: '1rem',
                background: '#f8f9fa',
                borderRadius: '8px'
              }}
            >
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                {feature.icon}
              </div>
              <h4 style={{ margin: '0 0 0.5rem 0', color: '#333' }}>
                {feature.title}
              </h4>
              <p style={{ margin: '0', color: '#666', fontSize: '0.85rem' }}>
                {feature.desc}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Usage Instructions */}
      <div style={{ 
        background: '#e7f3ff', 
        padding: '1.5rem', 
        borderRadius: '8px',
        marginBottom: '2rem'
      }}>
        <h2 style={{ marginTop: '0' }}>How to Use</h2>
        <ol style={{ paddingLeft: '1.5rem' }}>
          <li style={{ marginBottom: '0.5rem' }}>
            <strong>Join a Room:</strong> Click on any room above or use the quick join dropdown
          </li>
          <li style={{ marginBottom: '0.5rem' }}>
            <strong>Enter Username:</strong> You'll be prompted to enter a username when joining
          </li>
          <li style={{ marginBottom: '0.5rem' }}>
            <strong>Start Chatting:</strong> Type messages in the input field and press Enter
          </li>
          <li style={{ marginBottom: '0.5rem' }}>
            <strong>Voice Calls:</strong> Click the phone icon to start a voice call
          </li>
          <li style={{ marginBottom: '0.5rem' }}>
            <strong>Manage Users:</strong> Admins can kick users by clicking the × next to usernames
          </li>
        </ol>
      </div>

      {/* Technical Info */}
      <div style={{ 
        background: '#fff3cd', 
        padding: '1.5rem', 
        borderRadius: '8px',
        marginBottom: '2rem'
      }}>
        <h2 style={{ marginTop: '0' }}>Technical Requirements</h2>
        <ul style={{ paddingLeft: '1.5rem' }}>
          <li style={{ marginBottom: '0.5rem' }}>
            <strong>Backend:</strong> WebSocket server at <code>ws://localhost:3001/ws/chat/[roomId]</code>
          </li>
          <li style={{ marginBottom: '0.5rem' }}>
            <strong>Authentication:</strong> User must be logged in to access chat
          </li>
          <li style={{ marginBottom: '0.5rem' }}>
            <strong>Microphone:</strong> Required for voice calls (browser will prompt for permission)
          </li>
          <li style={{ marginBottom: '0.5rem' }}>
            <strong>HTTPS:</strong> Required in production for microphone access
          </li>
        </ul>
      </div>

      {/* Navigation */}
      <div style={{ textAlign: 'center' }}>
        <button 
          onClick={() => navigate('/chat-rooms')}
          style={{
            padding: '1rem 2rem',
            background: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '1rem',
            marginRight: '1rem'
          }}
        >
          Go to Chat Rooms
        </button>
        <button 
          onClick={() => navigate('/')}
          style={{
            padding: '1rem 2rem',
            background: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          Back to Home
        </button>
      </div>
    </div>
  );
};

export default ChatDemo;
