import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import UserAvatar, { GoogleUser, UserData } from './UserAvatar';

// Mock data
const mockGoogleUser: GoogleUser = {
  googleId: 'google_123',
  email: '<EMAIL>',
  name: 'Test User',
  picture: 'https://example.com/avatar.jpg'
};

const mockGoogleUserNoImage: GoogleUser = {
  googleId: 'google_456',
  email: '<EMAIL>',
  name: 'No Image User'
};

const mockUserData: UserData = {
  id: 'user_789',
  email: '<EMAIL>',
  name: 'Regular User',
  authMethod: 'email',
  isVerified: true
};

describe('UserAvatar Component', () => {
  test('renders with GoogleUser and picture', () => {
    render(<UserAvatar user={mockGoogleUser} />);
    
    const image = screen.getByRole('img');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', mockGoogleUser.picture);
    expect(image).toHaveAttribute('alt', mockGoogleUser.name);
  });

  test('renders initials when no picture provided', () => {
    render(<UserAvatar user={mockGoogleUserNoImage} />);
    
    const placeholder = screen.getByText('NU'); // "No Image User" -> "NU"
    expect(placeholder).toBeInTheDocument();
    expect(placeholder).toHaveClass('user-avatar__placeholder');
  });

  test('renders with UserData', () => {
    render(<UserAvatar user={mockUserData} />);
    
    const placeholder = screen.getByText('RU'); // "Regular User" -> "RU"
    expect(placeholder).toBeInTheDocument();
  });

  test('handles click events', () => {
    const handleClick = jest.fn();
    render(<UserAvatar user={mockGoogleUser} onClick={handleClick} />);
    
    const avatar = screen.getByRole('button');
    fireEvent.click(avatar);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('applies correct size classes', () => {
    const { rerender } = render(<UserAvatar user={mockGoogleUser} size="small" />);
    expect(screen.getByRole('img').closest('.user-avatar')).toHaveClass('user-avatar--small');

    rerender(<UserAvatar user={mockGoogleUser} size="large" />);
    expect(screen.getByRole('img').closest('.user-avatar')).toHaveClass('user-avatar--large');
  });

  test('shows online status when enabled', () => {
    render(<UserAvatar user={mockGoogleUser} showOnlineStatus={true} />);
    
    const statusIndicator = document.querySelector('.user-avatar__status--online');
    expect(statusIndicator).toBeInTheDocument();
  });

  test('handles single name initials', () => {
    const singleNameUser: GoogleUser = {
      googleId: 'single_123',
      email: '<EMAIL>',
      name: 'Madonna'
    };
    
    render(<UserAvatar user={singleNameUser} />);
    
    const placeholder = screen.getByText('M');
    expect(placeholder).toBeInTheDocument();
  });

  test('handles missing name gracefully', () => {
    const noNameUser: UserData = {
      id: 'no_name_123',
      email: '<EMAIL>',
      authMethod: 'email',
      isVerified: true
    };
    
    render(<UserAvatar user={noNameUser} />);
    
    const placeholder = screen.getByText('U'); // Default fallback
    expect(placeholder).toBeInTheDocument();
  });

  test('applies custom className', () => {
    render(<UserAvatar user={mockGoogleUser} className="custom-avatar" />);
    
    const avatar = screen.getByRole('img').closest('.user-avatar');
    expect(avatar).toHaveClass('custom-avatar');
  });

  test('uses custom alt text when provided', () => {
    render(<UserAvatar user={mockGoogleUser} alt="Custom alt text" />);
    
    const image = screen.getByRole('img');
    expect(image).toHaveAttribute('alt', 'Custom alt text');
  });
});
