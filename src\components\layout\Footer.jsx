import React from 'react';
import { Link } from 'react-router-dom';
import './Footer.scss';

const Footer = () => {
  return (
    <footer className="footer">
      <div className="container">
        <div className="footer__grid">
          <div className="footer__brand">
            <Link to="/" className="footer__logo">
              <span className="icon-studio icon-Studio-Project-1"></span>
              <span className="footer__logo-text">WolZyn</span>
            </Link>
            <p>Minimalist. Mythic. Meaningful.</p>
            <div className="footer__social">
              <a href="#" aria-label="Instagram">
                <i className="icon-instagram"></i>
              </a>
              <a href="#" aria-label="Twitter">
                <i className="icon-twitter"></i>
              </a>
              <a href="#" aria-label="Facebook">
                <i className="icon-facebook"></i>
              </a>
            </div>
          </div>
          
          <div className="footer__links">
            <h3>Shop</h3>
            <ul>
              <li><Link to="/coming-soon">All Products</Link></li>
              <li><Link to="/coming-soon">Design Drops</Link></li>
              <li><Link to="/coming-soon">Solid Colors</Link></li>
            </ul>
          </div>
          
          <div className="footer__links">
            <h3>About</h3>
            <ul>
              <li><Link to="/story">Our Story</Link></li>
              <li><Link to="/story">Mythology</Link></li>
              <li><Link to="/coming-soon">Contact</Link></li>
            </ul>
          </div>
          
          <div className="footer__newsletter">
            <h3>Join the Legend</h3>
            <p>Subscribe for updates on new drops and mythic tales.</p>
            <form className="footer__newsletter-form">
              <input 
                type="email" 
                className="footer__newsletter-input"
                placeholder="Your email address" 
                aria-label="Email address"
                required 
              />
              <button 
                type="submit" 
                className="footer__newsletter-button"
              >
                Subscribe
              </button>
            </form>
          </div>
        </div>
        
        <div className="footer__bottom">
          <p>&copy; {new Date().getFullYear()} WolZyn. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

