import React, { useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/layout/Layout';
import HomePage from './pages/Home';
import Home2Page from './pages/Home2';
import CollectionsPage from './pages/Collections';
import AboutPage from './pages/About';
import ComingSoonPage from './pages/ComingSoon';
import ProductPage from './pages/ProductPage';
import StorytellingProductView from './pages/StorytellingProductPage';
import Signup from './pages/Signup';
import AuthCallback from './pages/AuthCallback'; // Import the new component
import './App.scss';

function App() {
  const [theme, setTheme] = useState('spice');

  const changeTheme = (newTheme) => {
    setTheme(newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };

  return (
    <div className="app" data-theme={theme}>
      <Routes>
        <Route path="/" element={
          <Layout changeTheme={changeTheme} currentTheme={theme}>
            <HomePage />
          </Layout>
        } />
        {/* Home2 is a standalone page without the Layout wrapper */}
        <Route path="/home2" element={<Home2Page />} />
        <Route path="/collections" element={
          <Layout changeTheme={changeTheme} currentTheme={theme}>
            <CollectionsPage />
          </Layout>
        } />
        <Route path="/about" element={
          <Layout changeTheme={changeTheme} currentTheme={theme}>
            <AboutPage />
          </Layout>
        } />
        <Route path="/coming-soon" element={
          <Layout changeTheme={changeTheme} currentTheme={theme}>
            <ComingSoonPage />
          </Layout>
        } />
        {/* Add a redirect from /story to /coming-soon */}
        <Route path="/story" element={
          <Layout changeTheme={changeTheme} currentTheme={theme}>
            <ComingSoonPage />
          </Layout>
        } />
        {/* Add a redirect from /cart to /coming-soon */}
        <Route path="/cart" element={
          <Layout changeTheme={changeTheme} currentTheme={theme}>
            <ComingSoonPage />
          </Layout>
        } />
        {/* Original product page */}
        <Route path="/product/:id" element={
          <Layout changeTheme={changeTheme} currentTheme={theme}>
            <ProductPage />
          </Layout>
        } />
        {/* New storytelling product page */}
        <Route path="/storytelling-product/:id" element={
          <Layout changeTheme={changeTheme} currentTheme={theme}>
            <StorytellingProductView />
          </Layout>
        } />
        {/* Signup page */}
        <Route path="/signup" element={<Signup />} />
        {/* Login page - redirects to coming soon for now */}
        <Route path="/login" element={
          <Layout changeTheme={changeTheme} currentTheme={theme}>
            <ComingSoonPage />
          </Layout>
        } />
        {/* Auth callback route */}
        <Route path="/auth/success" element={<AuthCallback />} />
      </Routes>
    </div>
  );
}

export default App;









