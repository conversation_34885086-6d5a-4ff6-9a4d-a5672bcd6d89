.vr-viewer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  font-family: system-ui, sans-serif;

  &__header {
    text-align: center;
    margin-bottom: 2rem;
    color: white;

    h1 {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    p {
      font-size: 1.125rem;
      opacity: 0.9;
    }
  }

  &__controls {
    max-width: 800px;
    margin: 0 auto 2rem auto;
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  &__input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  &__input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;

    &:focus {
      outline: none;
      border-color: #667eea;
    }

    &::placeholder {
      color: #9ca3af;
    }
  }

  &__load-btn {
    padding: 0.75rem 2rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;

    &:hover:not(:disabled) {
      background: #5a67d8;
      transform: translateY(-1px);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  &__samples {
    h3 {
      margin: 0 0 1rem 0;
      color: #374151;
      font-size: 1.125rem;
    }
  }

  &__sample-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  &__sample-btn {
    padding: 0.5rem 1rem;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;

    &:hover {
      background: #e5e7eb;
      border-color: #9ca3af;
    }
  }

  &__error {
    max-width: 800px;
    margin: 0 auto 2rem auto;
    background: #fee2e2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
  }

  &__scene-container {
    max-width: 1200px;
    margin: 0 auto;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    min-height: 600px;
    position: relative;

    &--vr {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      max-width: none;
      border-radius: 0;
      z-index: 1000;
    }
  }

  &__video-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 600px;
    display: flex;
    flex-direction: column;
  }

  &__video {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background: #000;
  }

  &__video-controls {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    z-index: 10;
  }

  &__vr-btn {
    padding: 0.75rem 1.5rem;
    background: rgba(102, 126, 234, 0.9);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(90, 103, 216, 0.9);
      transform: translateY(-2px);
    }
  }

  &__placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 600px;
    background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
    color: white;
  }

  &__placeholder-content {
    text-align: center;
    max-width: 500px;
    padding: 2rem;

    h2 {
      font-size: 2rem;
      margin-bottom: 1rem;
      opacity: 0.9;
    }

    p {
      font-size: 1.125rem;
      margin-bottom: 2rem;
      opacity: 0.7;
    }
  }

  &__features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
  }

  &__feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
  }

  &__feature-icon {
    font-size: 1.5rem;
  }

  &__instructions {
    max-width: 800px;
    margin: 2rem auto 0 auto;
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 1rem 0;
      color: #374151;
      font-size: 1.25rem;
    }

    h4 {
      margin: 1.5rem 0 0.75rem 0;
      color: #374151;
      font-size: 1.125rem;
    }

    ol, ul {
      padding-left: 1.5rem;
      line-height: 1.6;
      color: #4b5563;
    }

    li {
      margin-bottom: 0.5rem;
    }
  }

  &__compatibility {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;

    ul {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 0.5rem;
      list-style: none;
      padding: 0;
    }

    li {
      padding: 0.5rem;
      background: #f9fafb;
      border-radius: 6px;
      text-align: center;
      font-size: 0.875rem;
    }
  }
}

// A-Frame specific styles
a-scene {
  width: 100% !important;
  height: 100% !important;
}

// Hide A-Frame UI in embedded mode
.a-enter-vr {
  bottom: 20px !important;
  right: 20px !important;
}

// Custom cursor styles
a-cursor {
  animation-duration: 0.15s !important;
}

// VR text styling
a-text {
  cursor: pointer;
}

// Responsive design
@media (max-width: 768px) {
  .vr-viewer {
    padding: 1rem;

    &__header h1 {
      font-size: 2rem;
    }

    &__controls {
      padding: 1.5rem;
    }

    &__scene-container {
      min-height: 400px;
    }

    &__placeholder {
      height: 400px;
    }

    &__instructions {
      padding: 1.5rem;
    }
  }
}
