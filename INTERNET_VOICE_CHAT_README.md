# 🌐 Internet Voice Chat System

A complete real-time voice chat system that allows users to connect and communicate across the internet using WebSocket servers and WebRTC technology.

## ✨ Features

- 🌍 **Internet-Wide Connectivity** - Users can connect from anywhere in the world
- 🎤 **Real-time Voice Chat** - WebRTC peer-to-peer voice communication
- 🔄 **Auto-Reconnection** - Automatic reconnection on connection loss
- 💪 **TURN Server Support** - Works behind firewalls and NAT
- 📱 **Cross-Platform** - Works on desktop, mobile, and tablets
- 🔧 **Dual Mode** - Switch between local (browser-only) and internet modes
- 🛡️ **Error Handling** - Comprehensive error handling and recovery

## 🚀 Quick Start

### 1. Start the WebSocket Server

```bash
# Navigate to server directory
cd server

# Install dependencies
npm install

# Start the server
npm start

# Or use the startup script
chmod +x start-server.sh
./start-server.sh
```

### 2. Configure the Frontend

```bash
# Copy environment file
cp .env.example .env

# Edit .env file
VITE_WEBSOCKET_URL=ws://localhost:3001
VITE_CHAT_MODE=internet
```

### 3. Start the Frontend

```bash
# Install dependencies (if not already done)
npm install

# Start development server
npm run dev
```

### 4. Test the Voice Chat

1. Open the app in multiple browsers/devices
2. Navigate to `/chat` or `/internet-chat`
3. Enter different usernames
4. Join voice chat and start talking!

## 🔧 Technical Architecture

### Server Components

#### WebSocket Server (`server/websocket-server.js`)
- **Real-time Communication** - Handles user connections and messaging
- **Room Management** - Manages chat rooms and user sessions
- **WebRTC Signaling** - Facilitates peer-to-peer connections
- **Auto Cleanup** - Removes inactive users automatically

#### Key Features:
- User session management
- Cross-room communication
- WebRTC offer/answer/ICE candidate exchange
- Heartbeat monitoring
- Error handling and recovery

### Frontend Components

#### Real WebSocket Service (`src/services/realWebSocket.ts`)
- **Server Connection** - Connects to real WebSocket server
- **Auto-Reconnection** - Handles connection drops
- **Message Routing** - Routes messages to appropriate handlers
- **Heartbeat System** - Maintains connection health

#### Internet WebRTC Service (`src/services/internetWebRTC.ts`)
- **Enhanced WebRTC** - Optimized for internet connectivity
- **TURN Server Support** - Works behind firewalls
- **Connection Retry** - Automatic retry on connection failure
- **Audio Processing** - Enhanced audio settings for internet

#### Internet Chat Hook (`src/hooks/useInternetChat.ts`)
- **State Management** - Manages chat and call state
- **Event Handling** - Handles WebSocket and WebRTC events
- **Error Recovery** - Comprehensive error handling

## 🌐 Deployment

### Local Development
```bash
# Server
cd server
npm start

# Frontend
npm run dev
```

### Production Deployment

#### Server Deployment (Node.js)
```bash
# Deploy to your preferred platform:
# - Heroku
# - DigitalOcean
# - AWS EC2
# - Google Cloud
# - Vercel (serverless)

# Example for Heroku:
git subtree push --prefix server heroku main
```

#### Frontend Deployment
```bash
# Build for production
npm run build

# Deploy to:
# - Netlify
# - Vercel
# - GitHub Pages
# - AWS S3 + CloudFront
```

#### Environment Variables

**Frontend (.env)**
```env
VITE_WEBSOCKET_URL=wss://your-server.com
VITE_CHAT_MODE=internet
```

**Server (Environment)**
```env
PORT=3001
NODE_ENV=production
```

## 🔒 Security Considerations

### WebSocket Security
- Use WSS (WebSocket Secure) in production
- Implement rate limiting
- Validate all incoming messages
- Use CORS properly

### WebRTC Security
- Use TURN servers with authentication
- Implement user authentication
- Monitor connection attempts
- Use HTTPS for signaling

## 🛠️ Configuration Options

### Chat Modes

#### Internet Mode (`VITE_CHAT_MODE=internet`)
- Connects to real WebSocket server
- Works across the internet
- Requires server infrastructure
- Best for production use

#### Local Mode (`VITE_CHAT_MODE=local`)
- Browser-only communication
- Works without server
- Limited to same browser/device
- Good for development/demos

### WebRTC Configuration

#### STUN Servers (Free)
```javascript
{ urls: 'stun:stun.l.google.com:19302' }
{ urls: 'stun:stun.services.mozilla.com' }
```

#### TURN Servers (For Firewall Traversal)
```javascript
{
  urls: 'turn:openrelay.metered.ca:80',
  username: 'openrelayproject',
  credential: 'openrelayproject'
}
```

### Custom TURN Server
```env
VITE_TURN_SERVER_URL=turn:your-server.com:3478
VITE_TURN_USERNAME=your-username
VITE_TURN_CREDENTIAL=your-password
```

## 📊 Monitoring and Health Checks

### Server Health Check
```bash
curl http://localhost:3001/health
```

Response:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "activeRooms": 1,
  "activeUsers": 3
}
```

### Room Information
```bash
curl http://localhost:3001/api/rooms/gamers
```

Response:
```json
{
  "roomId": "gamers",
  "userCount": 3,
  "callActive": true,
  "callParticipants": 2
}
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Connection Failed
- Check if server is running
- Verify WebSocket URL in .env
- Check firewall settings
- Ensure HTTPS for production

#### 2. No Audio
- Check microphone permissions
- Verify HTTPS connection
- Test with different browsers
- Check TURN server configuration

#### 3. Users Not Appearing
- Check WebSocket connection
- Verify server logs
- Test with different networks
- Check CORS settings

### Debug Tools

#### Frontend Debug Panel
- Available in development mode
- Shows connection status
- Displays user list
- Monitors call state

#### Server Logs
```bash
# View server logs
npm start

# Or with more verbose logging
DEBUG=* npm start
```

## 🚀 Performance Optimization

### Server Optimization
- Use clustering for multiple CPU cores
- Implement Redis for session storage
- Use load balancers for scaling
- Monitor memory usage

### Frontend Optimization
- Implement connection pooling
- Use audio compression
- Optimize WebRTC settings
- Cache user sessions

## 📈 Scaling Considerations

### Horizontal Scaling
- Multiple server instances
- Load balancer configuration
- Shared session storage (Redis)
- Database for persistent data

### Vertical Scaling
- Increase server resources
- Optimize memory usage
- Use faster networking
- Implement caching

## 🎯 Next Steps

1. **Deploy the Server** - Choose your hosting platform
2. **Configure DNS** - Set up domain and SSL
3. **Test Connectivity** - Verify across different networks
4. **Monitor Performance** - Set up logging and monitoring
5. **Scale as Needed** - Add more servers as users grow

## 🎉 Success!

You now have a complete internet-wide voice chat system that can:

- ✅ Connect users from anywhere in the world
- ✅ Handle real-time voice communication
- ✅ Automatically recover from connection issues
- ✅ Work behind firewalls and NAT
- ✅ Scale to support many users
- ✅ Provide comprehensive debugging tools

Perfect for:
- **Gaming Communities** - Voice chat for gamers
- **Remote Teams** - Team communication
- **Social Apps** - Community voice features
- **Educational Platforms** - Virtual classrooms
- **Customer Support** - Voice support systems

---

**🌐 Ready to connect the world? Start your server and let users talk!**
