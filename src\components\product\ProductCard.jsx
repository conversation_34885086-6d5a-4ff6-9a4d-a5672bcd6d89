import React from 'react';
import { Link } from 'react-router-dom';
import './ProductCard.scss';

const ProductCard = ({ product }) => {
  const { id, name, collection, description, image, price, isNew, isFeatured } = product;
  
  return (
    <div className="product-card">
      <div className="product-card__image-container">
        <img src={image} alt={name} className="product-card__image" />
        <div className="product-card__overlay">
          <Link to={`/product/${id}`} className="product-card__btn">Explore</Link>
        </div>
        {isNew && <span className="product-card__badge product-card__badge--new">New</span>}
        {isFeatured && <span className="product-card__badge product-card__badge--featured">Featured</span>}
      </div>
      <div className="product-card__info">
        <div className="product-card__collection">{collection}</div>
        <h3 className="product-card__name">
          <Link to={`/product/${id}`} className="product-card__name-link">
            {name}
          </Link>
        </h3>
        <p className="product-card__description">{description}</p>
        <div className="product-card__price">${price}</div>
      </div>
    </div>
  );
};

export default ProductCard;

