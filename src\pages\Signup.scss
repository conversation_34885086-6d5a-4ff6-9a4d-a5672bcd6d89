@use '../styles/variables' as *;
@use '../styles/mixins' as *;

.signup-page {
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary);
  padding: 1rem;
  position: relative;
  overflow: hidden;
  
  // Add a subtle pattern overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../assets/images/hero-bg.png');
    background-size: cover;
    background-position: center;
    opacity: 0.05;
    z-index: 0;
  }
  
  &__container {
    position: relative;
    width: 100%;
    max-width: 28rem;
    overflow: hidden;
    z-index: 1;
    border-radius: 1rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
    background-color: var(--bg-secondary);
  }
  
  &__decorative-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
  }
  
  &__decorative-circle {
    position: absolute;
    border-radius: 50%;
    z-index: 0;
    opacity: 0.5;
    
    &--top-right {
      top: -5rem;
      right: -5rem;
      width: 10rem;
      height: 10rem;
      background-color: var(--accent-primary);
    }
    
    &--bottom-left {
      bottom: -6rem;
      left: -6rem;
      width: 12rem;
      height: 12rem;
      background-color: var(--accent-secondary);
    }
  }
  
  &__form-container {
    position: relative;
    padding: 2.5rem;
    z-index: 2;
  }
  
  &__logo {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
    
    .icon-studio {
      font-size: 3rem;
      color: var(--accent-primary);
      transition: transform 0.3s ease;
      
      &:hover {
        transform: scale(1.05);
      }
    }
  }
  
  &__header {
    text-align: center;
    margin-bottom: 2rem;
    
    h2 {
      font-family: $font-serif;
      font-size: 2.25rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 0.5rem;
    }
    
    p {
      font-size: 1rem;
      color: var(--text-secondary);
    }
  }
  
  &__form {
    display: flex;
    flex-direction: column;
    gap: 1.75rem;
  }
  
  &__verification-info {
    text-align: center;
    margin-bottom: 0.5rem;
    
    p {
      color: var(--text-secondary);
      margin-bottom: 0.5rem;
    }
    
    .signup-page__contact-display {
      font-weight: 600;
      color: var(--text-primary);
      font-size: 1.125rem;
    }
  }
  
  &__contact-toggle {
    display: flex;
    width: 100%;
    border-radius: 0.5rem;
    overflow: hidden;
    margin-bottom: 0.5rem;
    border: 1px solid var(--border-color);
  }
  
  &__toggle-btn {
    flex: 1;
    padding: 0.75rem;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.active {
      background-color: var(--accent-primary);
      color: white;
    }
    
    &:hover:not(.active) {
      background-color: rgba(255, 255, 255, 0.05);
    }
  }
  
  &__input-group {
    position: relative;
    
    input {
      width: 100%;
      padding: 1rem;
      padding-top: 1.5rem;
      background-color: transparent;
      border: none;
      border-bottom: 2px solid var(--border-color);
      color: var(--text-primary);
      transition: all 0.3s ease;
      
      &:focus {
        outline: none;
        border-color: var(--accent-primary);
      }
      
      &::placeholder {
        color: transparent;
      }
      
      &:focus + label,
      &:not(:placeholder-shown) + label {
        transform: translateY(-1rem) scale(0.75);
        color: var(--accent-primary);
      }
    }
    
    label {
      position: absolute;
      left: 1rem;
      top: 1.25rem;
      color: var(--text-secondary);
      pointer-events: none;
      transform-origin: left top;
      transition: all 0.3s ease;
    }
    
    .error-message {
      font-size: 0.75rem;
      color: #ef4444;
      margin-top: 0.5rem;
    }
    
    .hint-message {
      font-size: 0.75rem;
      color: var(--text-secondary);
      margin-top: 0.5rem;
      opacity: 0.8;
    }
  }
  
  &__submit-button {
    padding: 0.875rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    color: white;
    background-color: var(--accent-primary);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    
    &:hover {
      background-color: var(--accent-secondary);
      transform: translateY(-2px);
      box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
  
  &__back-button {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 0.875rem;
    padding: 0.5rem;
    cursor: pointer;
    text-align: center;
    transition: color 0.3s ease;
    
    &:hover {
      color: var(--accent-primary);
    }
  }
  
  &__spinner-container {
    display: flex;
    align-items: center;
    justify-content: center;
    
    span {
      margin-left: 0.5rem;
    }
  }
  
  .spinner {
    width: 1.25rem;
    height: 1.25rem;
    animation: spin 1s linear infinite;
    
    &__track {
      fill: none;
      stroke: rgba(255, 255, 255, 0.3);
      stroke-width: 2;
    }
    
    &__path {
      fill: none;
      stroke: white;
      stroke-width: 2;
      stroke-linecap: round;
      stroke-dasharray: 60, 200;
      stroke-dashoffset: 0;
      animation: dash 1.5s ease-in-out infinite;
    }
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  @keyframes dash {
    0% {
      stroke-dasharray: 1, 200;
      stroke-dashoffset: 0;
    }
    50% {
      stroke-dasharray: 90, 200;
      stroke-dashoffset: -35;
    }
    100% {
      stroke-dasharray: 90, 200;
      stroke-dashoffset: -125;
    }
  }
  
  &__divider {
    position: relative;
    margin: 2rem 0;
    text-align: center;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background-color: var(--border-color);
    }
    
    span {
      position: relative;
      padding: 0 0.75rem;
      background-color: var(--bg-secondary);
      color: var(--text-secondary);
      font-size: 0.875rem;
    }
  }
  
  &__google-container {
    width: 100%;
    margin-bottom: 1rem;
  }
  
  &__google-button-container {
    width: 100%;
    min-height: 40px;
    
    // Style overrides for Google's rendered button
    div {
      width: 100% !important;
      border-radius: 0.5rem !important;
    }
  }
  
  &__google-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #dadce0;
    border-radius: 0.5rem;
    background-color: white;
    color: #3c4043;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, box-shadow 0.2s;
    
    &:hover {
      background-color: #f8f9fa;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
    
    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
  
  &__google-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
  
  &__footer {
    margin-top: 2rem;
    text-align: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
    
    a {
      color: var(--accent-primary);
      font-weight: 500;
      text-decoration: none;
      transition: color 0.3s ease;
      
      &:hover {
        color: var(--accent-secondary);
      }
    }
  }
}

// Animation keyframes
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0); }
  to { transform: scale(1); }
}

// Apply animations to elements
.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.scale-in {
  animation: scaleIn 0.8s ease-out forwards;
}

// Staggered animations
.stagger-item {
  opacity: 0;
  animation: slideUp 0.5s ease-out forwards;
  
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
  &:nth-child(5) { animation-delay: 0.5s; }
  &:nth-child(6) { animation-delay: 0.6s; }
}








