import React from 'react';
import './FogOverlay.scss';
import fogTexture from '../../assets/images/fog-1.png';

const FogOverlay = ({ opacity = 0.2, speed = 0.5 }) => {
  const fogStyle = {
    '--fog-opacity': opacity,
    '--fog-animation-duration': `${100 / speed}s`
  };
  
  return (
    <div className="fog-overlay" style={fogStyle}>
      <div className="fog-overlay__layer fog-overlay__layer--1">
        <div className="fog-overlay__image" style={{ backgroundImage: `url(${fogTexture})` }}></div>
      </div>
      <div className="fog-overlay__layer fog-overlay__layer--2">
        <div className="fog-overlay__image" style={{ backgroundImage: `url(${fogTexture})` }}></div>
      </div>
    </div>
  );
};

export default FogOverlay;