import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import ProductTopSection from '../components/product/ProductTopSection';
import ProductSelectionSection from '../components/product/ProductSelectionSection';
import ProductBottomSection from '../components/product/ProductBottomSection';
import './ProductPage.scss';

// Import default product image
import defaultProductImage from '../assets/images/product.png';

const ProductPage = () => {
  const { id } = useParams();
  const [product, setProduct] = useState(null);
  const [selectedSize, setSelectedSize] = useState(null);
  const [selectedColor, setSelectedColor] = useState(null);
  const [productImage, setProductImage] = useState(defaultProductImage);
  const [loading, setLoading] = useState(true);

  // Animation variants
  const fadeInVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: [0.6, 0.05, 0.01, 0.9],
        when: "beforeChildren"
      }
    }
  };

  const slideUpVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.6, 0.05, 0.01, 0.9]
      }
    }
  };
  // Mock product data - in a real app, this would come from an API
  const mockProducts = [
    {
      id: "1",
      name: "The Prometheus Tee",
      collection: "Rebellion",
      description: "Inspired by the titan who defied the gods to bring fire to humanity. This minimalist design captures the essence of rebellion against established order.",
      longDescription: "The Prometheus Tee embodies the spirit of defiance and the courage to challenge authority. Crafted from premium organic cotton with a tailored fit, this piece features subtle detailing that references the mythological titan's gift of fire to humanity. The minimalist design allows the wearer to carry this powerful narrative without ostentation.",
      price: 89.99,
      image: productImage,
      materials: "100% Organic Cotton",
      care: "Machine wash cold, tumble dry low",
      sizes: ["XS", "S", "M", "L", "XL", "XXL", "OVERSIZE"],
      colors: ["black", "navy", "red"],
      isNew: true,
      isFeatured: true
    },
    {
      id: "2",
      name: "Ascension Path",
      collection: "Ascension",
      description: "The journey from ordinary to extraordinary, woven into fabric.",
      longDescription: "The Ascension Path tee represents the human journey toward self-actualization. Each element of the design symbolizes a stage in personal growth, from the grounding elements at the base to the ethereal patterns that ascend toward the shoulders. Made from a blend of sustainable materials, this garment changes subtly with wear, developing a unique patina that reflects your personal journey.",
      price: 79.99,
      image: productImage,
      materials: "80% Organic Cotton, 20% Recycled Polyester",
      care: "Machine wash cold, hang dry",
      sizes: ["S", "M", "L", "XL", "XXL", "XXXL"],
      colors: ["black", "white"],
      isNew: false,
      isFeatured: true
    },
    {
      id: "3",
      name: "Oracle's Vision",
      collection: "Oracle",
      description: "Garments that carry the wisdom of those who see beyond the veil.",
      longDescription: "The Oracle's Vision tee channels the ancient wisdom of oracles who could perceive what others could not. The subtle pattern embedded in the fabric reveals different aspects depending on the light, symbolizing the changing nature of truth and perception. Crafted from our signature blend of organic cotton and modal for exceptional drape and comfort.",
      price: 94.99,
      image: productImage,
      materials: "70% Organic Cotton, 30% Modal",
      care: "Hand wash cold, lay flat to dry",
      sizes: ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
      colors: ["black", "gray"],
      isNew: true,
      isFeatured: true
    },
    {
      id: "7",
      name: "Ambitious Journey",
      price: 55,
      collection: "ambitious",
      colors: ["#D1CCC2", "#B8A38A", "#A38765", "#848871", "#A2AADB"],
      sizes: ["S", "M", "L", "XL"],
      description: "Embrace the path of growth with quiet determination and resilience.",
      longDescription: "The Ambitious Journey tee embodies the essence of purposeful progress. Featuring a minimalist design with subtle earth tones and a touch of lavender, this piece represents the balance between grounding stability and forward-thinking aspiration. Wear it as a reminder that true ambition comes from within.",
      isNew: true
    },
    {
      id: "8",
      name: "Sage Wisdom",
      price: 50,
      collection: "ambitious",
      colors: ["#848871", "#A2AADB", "#D1CCC2"],
      sizes: ["S", "M", "L", "XL"],
      description: "Channeling the calm clarity that comes from balanced ambition.",
      longDescription: "The Sage Wisdom tee celebrates the quiet confidence that comes from knowing your path. With its muted sage green base and subtle lavender accents, this design represents the harmony between ambition and wisdom. A reminder that true progress comes from thoughtful action rather than hasty movement.",
      isBestseller: true
    }
  ];
  useEffect(() => {
    // Simulate API fetch
    setTimeout(() => {
      const foundProduct = mockProducts.find(p => p.id === id);
      setProduct(foundProduct || null);
      if (foundProduct) {
        // Set default color to first available color
        setSelectedColor(foundProduct.colors[0]);

        // Set default image based on the first color
        if (foundProduct.colors && foundProduct.colors.length > 0) {
          const defaultColor = foundProduct.colors[0];
          handleImageChange(`${defaultColor}.png`);
        }
      }
      setLoading(false);
    }, 500);
  }, [id]);

  const handleColorSelect = (color) => {
    setSelectedColor(color);
  };

  const handleImageChange = (imageName) => {
    // In a real app, you would change the image based on the color
    // For now, we'll just keep the original image
    setProductImage(defaultProductImage);
  };

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  if (!product) {
    return <div className="not-found">Product not found</div>;
  }

  return (
    <motion.div
      className="product-page"
      initial="hidden"
      animate="visible"
      variants={fadeInVariants}
      layout
    >
      <div className="container">
        {/* Top Section: Product Image and Basic Info */}
        <ProductTopSection
          product={product}
          productImage={productImage}
          fadeInVariants={fadeInVariants}
          slideUpVariants={slideUpVariants}
        />

        {/* Middle Section: Color, Size Selection, and Add to Cart */}
        <ProductSelectionSection
          product={product}
          selectedSize={selectedSize}
          setSelectedSize={setSelectedSize}
          selectedColor={selectedColor}
          setSelectedColor={setSelectedColor}
          handleColorSelect={handleColorSelect}
          handleImageChange={handleImageChange}
          slideUpVariants={slideUpVariants}
        />

        {/* Bottom Section: Product Details */}
        <ProductBottomSection
          product={product}
          slideUpVariants={slideUpVariants}
        />
      </div>
    </motion.div>
  );
};

export default ProductPage;

