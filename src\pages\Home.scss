@use '../styles/variables' as *;
@use '../styles/mixins' as *;
@use '../styles/common';

// Hero Section
.hero {
  @extend .hero-section;
  min-height: 90vh;
  
  &::before {
    background-image: url('../assets/images/hero-bg.png');
    opacity: 0.5;
    @media (min-width: $breakpoint-lg) {
      width: 100%; // 50% width on desktop
    }
  }
  
  &__content {
    @extend .hero-section__content;
    // box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); // Removed box-shadow
    border-radius: 8px;
    backdrop-filter: blur(5px);
    padding: $spacing-xl;
  }
  
  &__title {
    @extend .hero-section__title;
    animation: fadeIn 0.8s ease-out, slideInUp 0.8s ease-out;
  }
  
  &__subtitle {
    @extend .hero-section__subtitle;
    animation: fadeIn 0.8s ease-out 0.3s both, slideInUp 0.8s ease-out 0.3s both;
  }
}

// Featured Collections Section
.featured-collections {
  padding: $spacing-xxl 0;
  
  &__header {
    text-align: center;
    margin-bottom: $spacing-xl;
  }
  
  &__title {
    font-size: 2.5rem;
    margin-bottom: $spacing-sm;
  }
  
  &__subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
  }
  
  &__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: $spacing-lg;
    
    @media (min-width: $breakpoint-md) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @media (min-width: $breakpoint-lg) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  &__item {
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--bg-secondary);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }
  }
  
  &__image {
    width: 100%;
    height: 300px;
    object-fit: cover;
  }
  
  &__content {
    padding: $spacing-lg;
  }
  
  &__item-title {
    font-size: 1.5rem;
    margin-bottom: $spacing-sm;
  }
  
  &__item-description {
    color: var(--text-secondary);
    margin-bottom: $spacing-md;
  }
  
  &__link {
    display: inline-block;
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    
    &:hover {
      color: var(--accent-secondary);
    }
    
    &::after {
      content: '→';
      margin-left: 5px;
      transition: transform 0.3s ease;
    }
    
    &:hover::after {
      transform: translateX(5px);
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
  }
  to {
    transform: translateY(0);
  }
}

// Brand Values Section
.brand-values {
  padding: $spacing-xl 0;
  background-color: var(--bg-secondary);
  
  &__title {
    @extend .section__title;
  }
  
  &__grid {
    @extend .grid;
    @extend .grid--3col;
  }
  
  &__item {
    text-align: center;
    padding: $spacing-lg;
    background-color: var(--bg-primary);
    border-radius: 8px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }
  }
  
  &__icon {
    font-size: 2.5rem;
    margin-bottom: $spacing-md;
    color: var(--accent-primary);
  }
  
  h3 {
    margin-bottom: $spacing-md;
    font-size: 1.5rem;
    font-family: $font-serif;
    color: var(--text-primary);
  }
  
  p {
    color: var(--text-secondary);
    line-height: 1.6;
  }
}

// Featured Capsules Section
.featured {
  padding: $spacing-xl 0;
  
  &__title {
    font-family: $font-serif;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: $spacing-xl;
    color: var(--text-primary);
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background-color: var(--accent-primary);
    }
    
    @media (max-width: $breakpoint-md) {
      font-size: 2rem;
      margin-bottom: $spacing-lg;
    }
  }
  
  &__grid {
    @extend .grid;
    @extend .grid--3col;
  }
  
  &__item {
    @extend .card;
    
    &:hover {
      .featured__image-container::after {
        opacity: 0.2;
      }
      
      .featured__image {
        transform: scale(1.05);
      }
    }
  }
  
  &__image-container {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1 / 1;
    margin-bottom: $spacing-md;
    border-radius: 4px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: var(--accent-primary);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }
  
  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.5s ease;
  }
  
  &__item-title {
    font-family: $font-serif;
    font-size: 1.5rem;
    margin-bottom: $spacing-xs;
    color: var(--text-primary);
  }
  
  &__item-desc {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
  }
}







