import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../hooks/useAuth';
import UserAvatar from '../components/common/UserAvatar';
import './Profile.scss';

const Profile: React.FC = () => {
  const navigate = useNavigate();
  const { 
    user, 
    isLoading, 
    isAuthenticated, 
    error, 
    updateProfile, 
    refreshProfile,
    clearError 
  } = useAuth();

  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    preferences: {
      theme: 'ascension',
      notifications: true,
      newsletter: false,
    }
  });
  const [updateError, setUpdateError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/signup');
    }
  }, [isLoading, isAuthenticated, navigate]);

  // Initialize form data when user data is loaded
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        preferences: {
          theme: user.preferences?.theme || 'ascension',
          notifications: user.preferences?.notifications ?? true,
          newsletter: user.preferences?.newsletter ?? false,
        }
      });
    }
  }, [user]);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (name.startsWith('preferences.')) {
      const prefKey = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        preferences: {
          ...prev.preferences,
          [prefKey]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    // Clear errors when user starts typing
    if (updateError) setUpdateError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdating(true);
    setUpdateError(null);

    try {
      await updateProfile({
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        preferences: formData.preferences
      });
      
      setIsEditing(false);
    } catch (error) {
      console.error('Profile update error:', error);
      setUpdateError(error instanceof Error ? error.message : 'Failed to update profile');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRefresh = async () => {
    try {
      await refreshProfile();
    } catch (error) {
      console.error('Profile refresh error:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="profile-page">
        <div className="profile-page__loading">
          <div className="spinner"></div>
          <p>Loading profile...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="profile-page">
        <div className="profile-page__error">
          <h2>Error Loading Profile</h2>
          <p>{error}</p>
          <button onClick={handleRefresh} className="btn btn-primary">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="profile-page">
        <div className="profile-page__error">
          <h2>Profile Not Found</h2>
          <p>Please log in to view your profile.</p>
          <button onClick={() => navigate('/signup')} className="btn btn-primary">
            Sign In
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="profile-page">
      <div className="container">
        <motion.div
          className="profile-page__content"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="profile-page__header">
            <div className="profile-page__avatar-section">
              <UserAvatar 
                user={user} 
                size="extra-large"
                showOnlineStatus={true}
              />
              <div className="profile-page__user-info">
                <h1>{user.name || 'WolZyn User'}</h1>
                <p className="profile-page__email">{user.email}</p>
                <span className={`profile-page__status profile-page__status--${user.isVerified ? 'verified' : 'unverified'}`}>
                  {user.isVerified ? 'Verified' : 'Unverified'}
                </span>
              </div>
            </div>
            
            <div className="profile-page__actions">
              <button 
                onClick={handleRefresh}
                className="btn btn-secondary"
                disabled={isLoading}
              >
                Refresh
              </button>
              <button 
                onClick={() => setIsEditing(!isEditing)}
                className="btn btn-primary"
              >
                {isEditing ? 'Cancel' : 'Edit Profile'}
              </button>
            </div>
          </div>

          {updateError && (
            <div className="profile-page__error-message">
              {updateError}
            </div>
          )}

          <div className="profile-page__details">
            {isEditing ? (
              <form onSubmit={handleSubmit} className="profile-page__form">
                <div className="profile-page__form-group">
                  <label htmlFor="name">Full Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter your full name"
                  />
                </div>

                <div className="profile-page__form-group">
                  <label htmlFor="email">Email</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter your email"
                  />
                </div>

                <div className="profile-page__form-group">
                  <label htmlFor="phone">Phone</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Enter your phone number"
                  />
                </div>

                <div className="profile-page__form-group">
                  <label htmlFor="theme">Theme Preference</label>
                  <select
                    id="theme"
                    name="preferences.theme"
                    value={formData.preferences.theme}
                    onChange={handleInputChange}
                  >
                    <option value="ascension">Ascension</option>
                    <option value="ember">Ember</option>
                    <option value="spice">Spice</option>
                  </select>
                </div>

                <div className="profile-page__form-group profile-page__form-group--checkbox">
                  <label>
                    <input
                      type="checkbox"
                      name="preferences.notifications"
                      checked={formData.preferences.notifications}
                      onChange={handleInputChange}
                    />
                    Enable notifications
                  </label>
                </div>

                <div className="profile-page__form-group profile-page__form-group--checkbox">
                  <label>
                    <input
                      type="checkbox"
                      name="preferences.newsletter"
                      checked={formData.preferences.newsletter}
                      onChange={handleInputChange}
                    />
                    Subscribe to newsletter
                  </label>
                </div>

                <div className="profile-page__form-actions">
                  <button
                    type="button"
                    onClick={() => setIsEditing(false)}
                    className="btn btn-secondary"
                    disabled={isUpdating}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={isUpdating}
                  >
                    {isUpdating ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </form>
            ) : (
              <div className="profile-page__info">
                <div className="profile-page__info-item">
                  <label>Authentication Method</label>
                  <span className="profile-page__auth-method">
                    {user.authMethod.charAt(0).toUpperCase() + user.authMethod.slice(1)}
                  </span>
                </div>
                
                <div className="profile-page__info-item">
                  <label>Member Since</label>
                  <span>{user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown'}</span>
                </div>

                <div className="profile-page__info-item">
                  <label>Theme</label>
                  <span>{user.preferences?.theme || 'Default'}</span>
                </div>

                <div className="profile-page__info-item">
                  <label>Notifications</label>
                  <span>{user.preferences?.notifications ? 'Enabled' : 'Disabled'}</span>
                </div>

                <div className="profile-page__info-item">
                  <label>Newsletter</label>
                  <span>{user.preferences?.newsletter ? 'Subscribed' : 'Not subscribed'}</span>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Profile;
