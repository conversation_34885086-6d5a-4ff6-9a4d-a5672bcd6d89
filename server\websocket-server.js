const WebSocket = require('ws');
const http = require('http');
const express = require('express');
const cors = require('cors');

const app = express();
app.use(cors());
app.use(express.json());

const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// Store active rooms and users
const rooms = new Map();
const userSessions = new Map();

// Message types
const MessageType = {
  USER_JOIN: 'user_join',
  USER_LEAVE: 'user_leave',
  USER_LIST: 'user_list',
  CALL_START: 'call_start',
  CALL_END: 'call_end',
  CALL_JOIN: 'call_join',
  CALL_LEAVE: 'call_leave',
  OFFER: 'offer',
  ANSWER: 'answer',
  ICE_CANDIDATE: 'ice_candidate',
  ERROR: 'error',
  HEARTBEAT: 'heartbeat'
};

class ChatRoom {
  constructor(roomId) {
    this.roomId = roomId;
    this.users = new Map();
    this.callActive = false;
    this.callParticipants = new Set();
  }

  addUser(userId, userData) {
    this.users.set(userId, {
      ...userData,
      lastSeen: Date.now()
    });
    console.log(`User ${userData.username} joined room ${this.roomId}`);
  }

  removeUser(userId) {
    const user = this.users.get(userId);
    if (user) {
      this.users.delete(userId);
      this.callParticipants.delete(userId);
      console.log(`User ${user.username} left room ${this.roomId}`);
    }
  }

  getUserList() {
    return Array.from(this.users.values());
  }

  updateUserCallStatus(userId, isInCall, isMuted = false) {
    const user = this.users.get(userId);
    if (user) {
      user.isInCall = isInCall;
      user.isMuted = isMuted;
      user.lastSeen = Date.now();
      
      if (isInCall) {
        this.callParticipants.add(userId);
      } else {
        this.callParticipants.delete(userId);
      }
    }
  }

  broadcastToRoom(message, excludeUserId = null) {
    this.users.forEach((user, userId) => {
      if (userId !== excludeUserId) {
        const session = userSessions.get(userId);
        if (session && session.ws.readyState === WebSocket.OPEN) {
          session.ws.send(JSON.stringify(message));
        }
      }
    });
  }

  cleanupInactiveUsers() {
    const now = Date.now();
    const timeout = 30000; // 30 seconds timeout
    
    for (const [userId, user] of this.users.entries()) {
      if (now - user.lastSeen > timeout) {
        console.log(`Cleaning up inactive user: ${user.username}`);
        this.removeUser(userId);
        
        // Notify other users
        this.broadcastToRoom({
          type: MessageType.USER_LEAVE,
          payload: { userId, username: user.username },
          timestamp: new Date().toISOString()
        });
      }
    }
  }
}

function getOrCreateRoom(roomId) {
  if (!rooms.has(roomId)) {
    rooms.set(roomId, new ChatRoom(roomId));
  }
  return rooms.get(roomId);
}

function handleMessage(ws, message, userId) {
  try {
    const data = JSON.parse(message);
    const { type, payload, roomId } = data;
    
    console.log(`Received ${type} from user ${userId} in room ${roomId}`);
    
    const room = getOrCreateRoom(roomId);
    const session = userSessions.get(userId);
    
    switch (type) {
      case MessageType.USER_JOIN:
        room.addUser(userId, payload);
        session.roomId = roomId;
        
        // Send current user list to the new user
        ws.send(JSON.stringify({
          type: MessageType.USER_LIST,
          payload: room.getUserList(),
          timestamp: new Date().toISOString()
        }));
        
        // Broadcast user join to others
        room.broadcastToRoom({
          type: MessageType.USER_JOIN,
          payload: payload,
          timestamp: new Date().toISOString()
        }, userId);
        break;

      case MessageType.CALL_START:
        room.callActive = true;
        room.updateUserCallStatus(userId, true);
        room.broadcastToRoom({
          type: MessageType.CALL_START,
          payload: { userId, ...payload },
          timestamp: new Date().toISOString()
        });
        break;

      case MessageType.CALL_JOIN:
        room.updateUserCallStatus(userId, true);
        room.broadcastToRoom({
          type: MessageType.CALL_JOIN,
          payload: { userId, ...payload },
          timestamp: new Date().toISOString()
        });
        break;

      case MessageType.CALL_LEAVE:
        room.updateUserCallStatus(userId, false);
        room.broadcastToRoom({
          type: MessageType.CALL_LEAVE,
          payload: { userId, ...payload },
          timestamp: new Date().toISOString()
        });
        break;

      case MessageType.OFFER:
      case MessageType.ANSWER:
      case MessageType.ICE_CANDIDATE:
        // Forward WebRTC signaling messages to target user
        const targetUserId = payload.targetUserId;
        const targetSession = userSessions.get(targetUserId);
        
        if (targetSession && targetSession.ws.readyState === WebSocket.OPEN) {
          targetSession.ws.send(JSON.stringify({
            type,
            payload: {
              ...payload,
              fromUserId: userId
            },
            timestamp: new Date().toISOString()
          }));
        }
        break;

      case MessageType.HEARTBEAT:
        // Update last seen timestamp
        const user = room.users.get(userId);
        if (user) {
          user.lastSeen = Date.now();
        }
        
        // Send heartbeat response
        ws.send(JSON.stringify({
          type: MessageType.HEARTBEAT,
          payload: { status: 'ok' },
          timestamp: new Date().toISOString()
        }));
        break;

      default:
        console.log(`Unknown message type: ${type}`);
    }
  } catch (error) {
    console.error('Error handling message:', error);
    ws.send(JSON.stringify({
      type: MessageType.ERROR,
      payload: { message: 'Invalid message format' },
      timestamp: new Date().toISOString()
    }));
  }
}

function handleDisconnection(userId) {
  const session = userSessions.get(userId);
  if (session && session.roomId) {
    const room = rooms.get(session.roomId);
    if (room) {
      const user = room.users.get(userId);
      room.removeUser(userId);
      
      // Notify other users
      room.broadcastToRoom({
        type: MessageType.USER_LEAVE,
        payload: { 
          userId, 
          username: user ? user.username : 'Unknown User' 
        },
        timestamp: new Date().toISOString()
      });
    }
  }
  
  userSessions.delete(userId);
  console.log(`User ${userId} disconnected`);
}

// WebSocket connection handler
wss.on('connection', (ws, req) => {
  const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  console.log(`New connection: ${userId}`);
  
  // Store user session
  userSessions.set(userId, {
    ws,
    userId,
    roomId: null,
    connectedAt: Date.now()
  });

  // Send connection confirmation
  ws.send(JSON.stringify({
    type: 'connection_established',
    payload: { userId },
    timestamp: new Date().toISOString()
  }));

  // Handle incoming messages
  ws.on('message', (message) => {
    handleMessage(ws, message, userId);
  });

  // Handle disconnection
  ws.on('close', () => {
    handleDisconnection(userId);
  });

  // Handle errors
  ws.on('error', (error) => {
    console.error(`WebSocket error for user ${userId}:`, error);
    handleDisconnection(userId);
  });
});

// Cleanup inactive users periodically
setInterval(() => {
  rooms.forEach(room => {
    room.cleanupInactiveUsers();
  });
}, 15000); // Every 15 seconds

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    activeRooms: rooms.size,
    activeUsers: userSessions.size
  });
});

// Get room info endpoint
app.get('/api/rooms/:roomId', (req, res) => {
  const room = rooms.get(req.params.roomId);
  if (room) {
    res.json({
      roomId: req.params.roomId,
      userCount: room.users.size,
      callActive: room.callActive,
      callParticipants: room.callParticipants.size
    });
  } else {
    res.status(404).json({ error: 'Room not found' });
  }
});

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 WebSocket server running on port ${PORT}`);
  console.log(`📡 WebSocket endpoint: ws://localhost:${PORT}/ws/chat/gamers`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

module.exports = { app, server, wss };
