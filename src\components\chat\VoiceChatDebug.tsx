import React, { useState, useEffect } from 'react';
import { useChat } from '../../hooks/useChat';

const VoiceChatDebug: React.FC = () => {
  const {
    users,
    isConnected,
    connectionError,
    callState,
    connect,
    disconnect,
    startCall,
    joinCall,
    leaveCall,
    toggleMute
  } = useChat();

  const [username, setUsername] = useState('');
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [isConnecting, setIsConnecting] = useState(false);

  const addDebugInfo = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugInfo(prev => [...prev.slice(-9), `[${timestamp}] ${message}`]);
  };

  useEffect(() => {
    if (isConnected) {
      addDebugInfo(`Connected successfully. Users: ${users.length}`);
    }
  }, [isConnected, users.length]);

  useEffect(() => {
    if (connectionError) {
      addDebugInfo(`Connection error: ${connectionError}`);
    }
  }, [connectionError]);

  useEffect(() => {
    addDebugInfo(`Call state changed: ${JSON.stringify(callState)}`);
  }, [callState]);

  const handleConnect = async () => {
    if (!username.trim()) return;
    
    setIsConnecting(true);
    addDebugInfo(`Attempting to connect as: ${username}`);
    
    try {
      await connect(username.trim());
      addDebugInfo('Connection successful');
    } catch (error) {
      addDebugInfo(`Connection failed: ${error}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleJoinCall = async () => {
    addDebugInfo('Attempting to join call...');
    try {
      await joinCall();
      addDebugInfo('Joined call successfully');
    } catch (error) {
      addDebugInfo(`Join call failed: ${error}`);
    }
  };

  const handleStartCall = async () => {
    addDebugInfo('Attempting to start call...');
    try {
      await startCall();
      addDebugInfo('Started call successfully');
    } catch (error) {
      addDebugInfo(`Start call failed: ${error}`);
    }
  };

  const handleLeaveCall = () => {
    addDebugInfo('Leaving call...');
    leaveCall();
  };

  const handleToggleMute = () => {
    addDebugInfo(`Toggling mute (currently ${callState.isMuted ? 'muted' : 'unmuted'})`);
    toggleMute();
  };

  const clearDebugInfo = () => {
    setDebugInfo([]);
  };

  return (
    <div style={{ 
      padding: '1rem', 
      border: '2px solid #ddd', 
      borderRadius: '8px',
      margin: '1rem',
      backgroundColor: '#f9f9f9',
      fontFamily: 'monospace'
    }}>
      <h3 style={{ margin: '0 0 1rem 0', color: '#333' }}>🔧 Voice Chat Debug Panel</h3>
      
      {/* Connection Section */}
      <div style={{ marginBottom: '1rem', padding: '1rem', backgroundColor: 'white', borderRadius: '4px' }}>
        <h4 style={{ margin: '0 0 0.5rem 0' }}>Connection</h4>
        <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center', marginBottom: '0.5rem' }}>
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="Enter username"
            disabled={isConnected}
            style={{ padding: '0.25rem', flex: 1 }}
          />
          <button
            onClick={handleConnect}
            disabled={isConnecting || isConnected || !username.trim()}
            style={{ padding: '0.25rem 0.5rem' }}
          >
            {isConnecting ? 'Connecting...' : 'Connect'}
          </button>
          <button
            onClick={disconnect}
            disabled={!isConnected}
            style={{ padding: '0.25rem 0.5rem' }}
          >
            Disconnect
          </button>
        </div>
        <div style={{ fontSize: '0.8rem', color: isConnected ? 'green' : 'red' }}>
          Status: {isConnected ? 'Connected' : 'Disconnected'}
        </div>
      </div>

      {/* Voice Call Section */}
      {isConnected && (
        <div style={{ marginBottom: '1rem', padding: '1rem', backgroundColor: 'white', borderRadius: '4px' }}>
          <h4 style={{ margin: '0 0 0.5rem 0' }}>Voice Call</h4>
          <div style={{ display: 'flex', gap: '0.5rem', marginBottom: '0.5rem' }}>
            <button
              onClick={handleStartCall}
              disabled={callState.isInCall}
              style={{ padding: '0.25rem 0.5rem' }}
            >
              Start Call
            </button>
            <button
              onClick={handleJoinCall}
              disabled={callState.isInCall}
              style={{ padding: '0.25rem 0.5rem' }}
            >
              Join Call
            </button>
            <button
              onClick={handleLeaveCall}
              disabled={!callState.isInCall}
              style={{ padding: '0.25rem 0.5rem' }}
            >
              Leave Call
            </button>
            <button
              onClick={handleToggleMute}
              disabled={!callState.isInCall}
              style={{ 
                padding: '0.25rem 0.5rem',
                backgroundColor: callState.isMuted ? '#ff6b6b' : '#51cf66',
                color: 'white'
              }}
            >
              {callState.isMuted ? 'Unmute' : 'Mute'}
            </button>
          </div>
          <div style={{ fontSize: '0.8rem' }}>
            <div>In Call: {callState.isInCall ? 'Yes' : 'No'}</div>
            <div>Muted: {callState.isMuted ? 'Yes' : 'No'}</div>
            <div>Participants: {callState.participants.length}</div>
          </div>
        </div>
      )}

      {/* Users Section */}
      {isConnected && (
        <div style={{ marginBottom: '1rem', padding: '1rem', backgroundColor: 'white', borderRadius: '4px' }}>
          <h4 style={{ margin: '0 0 0.5rem 0' }}>Users ({users.length})</h4>
          <div style={{ fontSize: '0.8rem' }}>
            {users.length === 0 ? (
              <div style={{ color: '#666' }}>No users found</div>
            ) : (
              users.map(user => (
                <div key={user.id} style={{ 
                  padding: '0.25rem', 
                  backgroundColor: user.isInCall ? '#e8f5e8' : '#f5f5f5',
                  margin: '0.25rem 0',
                  borderRadius: '2px'
                }}>
                  <strong>{user.username}</strong> 
                  {user.isInCall && <span style={{ color: 'green' }}> 🔊</span>}
                  {user.isMuted && <span style={{ color: 'red' }}> 🔇</span>}
                  <div style={{ fontSize: '0.7rem', color: '#666' }}>
                    ID: {user.id.slice(-8)}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Debug Log */}
      <div style={{ padding: '1rem', backgroundColor: 'white', borderRadius: '4px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
          <h4 style={{ margin: 0 }}>Debug Log</h4>
          <button onClick={clearDebugInfo} style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}>
            Clear
          </button>
        </div>
        <div style={{ 
          height: '200px', 
          overflow: 'auto', 
          backgroundColor: '#f5f5f5', 
          padding: '0.5rem',
          fontSize: '0.75rem',
          fontFamily: 'monospace'
        }}>
          {debugInfo.length === 0 ? (
            <div style={{ color: '#666' }}>No debug info yet...</div>
          ) : (
            debugInfo.map((info, index) => (
              <div key={index} style={{ marginBottom: '0.25rem' }}>
                {info}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default VoiceChatDebug;
