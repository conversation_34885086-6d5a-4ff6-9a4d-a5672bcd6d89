import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import Cookies from 'js-cookie';
import { authApi, UserProfile, ApiError, getAuthToken } from '../services/api';

// <PERSON>ie names
const USER_COOKIE_NAME = 'wolzyn_user';
const TOKEN_COOKIE_NAME = 'wolzyn_token';

interface AuthState {
  user: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

interface AuthActions {
  fetchProfile: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  refreshProfile: () => Promise<void>;
}

export interface UseAuthReturn extends AuthState, AuthActions {}

export const useAuth = (): UseAuthReturn => {
  const navigate = useNavigate();
  const [state, setState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    error: null,
  });

  // Load user from cookie on mount
  useEffect(() => {
    const loadUserFromCookie = () => {
      try {
        const userCookie = Cookies.get(USER_COOKIE_NAME);
        const token = getAuthToken();

        if (userCookie && token) {
          const userData = JSON.parse(userCookie);
          setState(prev => ({
            ...prev,
            user: userData,
            isAuthenticated: true,
            isLoading: false,
          }));
        } else {
          setState(prev => ({
            ...prev,
            isLoading: false,
          }));
        }
      } catch (error) {
        console.error('Error loading user from cookie:', error);
        // Clear invalid cookie data
        Cookies.remove(USER_COOKIE_NAME);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Invalid user data in storage',
        }));
      }
    };

    loadUserFromCookie();
  }, []);

  // Fetch user profile from backend
  const fetchProfile = useCallback(async (): Promise<void> => {
    const token = getAuthToken();
    
    if (!token) {
      setState(prev => ({
        ...prev,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: 'No authentication token found',
      }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const userProfile = await authApi.getProfile();
      
      setState(prev => ({
        ...prev,
        user: userProfile,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
      
      let errorMessage = 'Failed to load user profile';
      
      if (error instanceof ApiError) {
        if (error.status === 401) {
          errorMessage = 'Session expired. Please log in again.';
          // Clear local auth data
          Cookies.remove(USER_COOKIE_NAME);
          Cookies.remove(TOKEN_COOKIE_NAME);
        } else {
          errorMessage = error.message;
        }
      }

      setState(prev => ({
        ...prev,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, []);

  // Update user profile
  const updateProfile = useCallback(async (updates: Partial<UserProfile>): Promise<void> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const updatedProfile = await authApi.updateProfile(updates);
      
      setState(prev => ({
        ...prev,
        user: updatedProfile,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      console.error('Failed to update user profile:', error);
      
      const errorMessage = error instanceof ApiError 
        ? error.message 
        : 'Failed to update profile';

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      
      throw error; // Re-throw so calling component can handle it
    }
  }, []);

  // Logout user
  const logout = useCallback(async (): Promise<void> => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      await authApi.logout();
    } catch (error) {
      console.error('Logout error:', error);
      // Continue with local logout even if backend call fails
    } finally {
      // Clear local state and cookies
      setState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
      
      // Redirect to home page
      navigate('/');
    }
  }, [navigate]);

  // Clear error state
  const clearError = useCallback((): void => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Refresh profile data from backend
  const refreshProfile = useCallback(async (): Promise<void> => {
    if (!state.isAuthenticated) {
      return;
    }
    
    await fetchProfile();
  }, [fetchProfile, state.isAuthenticated]);

  // Auto-fetch profile when token is available but user data is not
  useEffect(() => {
    const token = getAuthToken();
    
    if (token && !state.user && !state.isLoading && !state.error) {
      fetchProfile();
    }
  }, [fetchProfile, state.user, state.isLoading, state.error]);

  return {
    // State
    user: state.user,
    isLoading: state.isLoading,
    isAuthenticated: state.isAuthenticated,
    error: state.error,
    
    // Actions
    fetchProfile,
    updateProfile,
    logout,
    clearError,
    refreshProfile,
  };
};
