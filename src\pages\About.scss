@use '../styles/variables' as *;
@use '../styles/mixins' as *;

.about-page {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

// Hero Section
.about-hero {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  background-color: var(--bg-secondary);
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../assets/images/hero-bg.png');
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    z-index: 0;
  }
  
  &__content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    padding: $spacing-xl 0;
    @include fade-in(0.8s);
  }
  
  &__title {
    font-family: $font-serif;
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: $spacing-md;
    @include slide-in('up', 30px, 0.8s);
    
    @include mobile {
      font-size: 2.5rem;
    }
  }
  
  &__subtitle {
    font-family: $font-primary;
    font-size: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
    color: var(--text-secondary);
    @include slide-in('up', 20px, 1s);
    
    @include mobile {
      font-size: 1.25rem;
    }
  }
}

// Section Title
.section-title {
  font-family: $font-serif;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: $spacing-lg;
  position: relative;
  
  &::after {
    content: '';
    display: block;
    width: 60px;
    height: 2px;
    background-color: var(--accent-primary);
    margin-top: $spacing-sm;
  }
  
  &.centered {
    text-align: center;
    
    &::after {
      margin-left: auto;
      margin-right: auto;
    }
  }
  
  @include mobile {
    font-size: 2rem;
  }
}

// Vision Section
.about-vision {
  padding: $spacing-xl 0;
  
  &__grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-xl;
    align-items: center;
    
    @include mobile {
      grid-template-columns: 1fr;
    }
  }
  
  &__content {
    @include fade-in(0.8s);
  }
  
  &__text {
    margin-bottom: $spacing-md;
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-secondary);
  }
  
  &__quote {
    margin-top: $spacing-lg;
    padding-left: $spacing-md;
    border-left: 3px solid var(--accent-primary);
    
    blockquote {
      font-family: $font-serif;
      font-style: italic;
      font-size: 1.3rem;
      margin-bottom: $spacing-xs;
      color: var(--text-primary);
    }
    
    cite {
      font-style: normal;
      color: var(--text-secondary);
    }
  }
  
  &__image-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    @include fade-in(1s);
    @include slide-in('left', 50px, 1s);
    
    @include mobile {
      margin-top: $spacing-lg;
    }
  }
  
  &__image {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
    
    &:hover {
      transform: scale(1.03);
    }
  }
}

// Philosophy Section
.about-philosophy {
  padding: $spacing-xl 0;
  background-color: var(--bg-secondary);
  
  &__grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: $spacing-lg;
    margin-top: $spacing-xl;
    
    @include mobile {
      grid-template-columns: 1fr;
    }
  }
  
  &__item {
    text-align: center;
    padding: $spacing-lg;
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    @include fade-in(0.8s);
    
    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    }
    
    &:nth-child(1) {
      animation-delay: 0.2s;
    }
    
    &:nth-child(2) {
      animation-delay: 0.4s;
    }
    
    &:nth-child(3) {
      animation-delay: 0.6s;
    }
  }
  
  &__icon {
    font-size: 2.5rem;
    margin-bottom: $spacing-md;
  }
  
  h3 {
    font-family: $font-serif;
    font-size: 1.5rem;
    margin-bottom: $spacing-sm;
  }
  
  p {
    color: var(--text-secondary);
    line-height: 1.6;
  }
}

// Process Section
.about-process {
  padding: $spacing-xl 0;
  
  &__grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-xl;
    align-items: center;
    
    @include mobile {
      grid-template-columns: 1fr;
    }
  }
  
  &__image-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    @include fade-in(1s);
    @include slide-in('right', 50px, 1s);
    
    @include mobile {
      order: 2;
      margin-top: $spacing-lg;
    }
  }
  
  &__image {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
    
    &:hover {
      transform: scale(1.03);
    }
  }
  
  &__content {
    @include fade-in(0.8s);
    
    @include mobile {
      order: 1;
    }
  }
  
  &__steps {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }
  
  &__step {
    position: relative;
    padding-left: 60px;
    
    &-number {
      position: absolute;
      left: 0;
      top: 0;
      font-family: $font-serif;
      font-size: 2rem;
      font-weight: 700;
      color: var(--accent-primary);
      opacity: 0.8;
    }
    
    h3 {
      font-family: $font-serif;
      font-size: 1.3rem;
      margin-bottom: $spacing-xs;
    }
    
    p {
      color: var(--text-secondary);
      line-height: 1.6;
    }
  }
}

// Studio Section
.about-studio {
  padding: $spacing-xl 0;
  background-color: var(--bg-secondary);
  
  &__grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-xl;
    align-items: center;
    
    @include mobile {
      grid-template-columns: 1fr;
    }
  }
  
  &__content {
    @include fade-in(0.8s);
  }
  
  &__text {
    margin-bottom: $spacing-md;
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-secondary);
  }
  
  &__image-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    @include fade-in(1s);
    @include slide-in('left', 50px, 1s);
    
    @include mobile {
      margin-top: $spacing-lg;
    }
  }
  
  &__image {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
    
    &:hover {
      transform: scale(1.03);
    }
  }
}

// Join Section
.about-join {
  padding: $spacing-xl 0;
  background-color: var(--accent-primary);
  color: white;
  
  &__content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    @include fade-in(0.8s);
  }
  
  &__title {
    font-family: $font-serif;
    font-size: 2.5rem;
    margin-bottom: $spacing-md;
    
    @include mobile {
      font-size: 2rem;
    }
  }
  
  &__text {
    font-size: 1.2rem;
    margin-bottom: $spacing-lg;
    opacity: 0.9;
    
    @include mobile {
      font-size: 1.1rem;
    }
  }
  
  &__buttons {
    display: flex;
    justify-content: center;
    gap: $spacing-md;
    
    .btn-primary {
      background-color: white;
      color: var(--accent-primary);
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.9);
      }
    }
    
    .btn-secondary {
      border-color: white;
      color: white;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
    
    @include mobile {
      flex-direction: column;
      gap: $spacing-sm;
    }
  }
}